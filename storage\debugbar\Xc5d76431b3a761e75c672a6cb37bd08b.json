{"__meta": {"id": "Xc5d76431b3a761e75c672a6cb37bd08b", "datetime": "2024-11-28 17:06:37", "utime": **********.628299, "method": "POST", "uri": "/playfiver/webhook", "ip": "************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732824390.454615, "end": **********.628326, "duration": 7.173710823059082, "duration_str": "7.17s", "measures": [{"label": "Booting", "start": 1732824390.454615, "relative_start": 0, "end": 1732824392.271047, "relative_end": 1732824392.271047, "duration": 1.816431999206543, "duration_str": "1.82s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732824392.271067, "relative_start": 1.8164517879486084, "end": **********.62833, "relative_end": 4.0531158447265625e-06, "duration": 5.357263088226318, "duration_str": "5.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14085296, "peak_usage_str": "13MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST playfiver/webhook", "middleware": "web", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@webhookPlayFiver", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=424\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:424-427</a>"}, "queries": {"nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07163, "accumulated_duration_str": "71.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `games_keys` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 29}, {"index": 20, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 98}, {"index": 21, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 66}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 426}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}], "start": **********.096609, "duration": 0.01013, "duration_str": "10.13ms", "memory": 0, "memory_str": null, "filename": "PlayFiverTrait.php:29", "source": {"index": 19, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FTraits%2FProviders%2FPlayFiverTrait.php&line=29", "ajax": false, "filename": "PlayFiverTrait.php", "line": "29"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 14.142}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 99}, {"index": 17, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 66}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 426}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}], "start": **********.18244, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "PlayFiverTrait.php:99", "source": {"index": 16, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FTraits%2FProviders%2FPlayFiverTrait.php&line=99", "ajax": false, "filename": "PlayFiverTrait.php", "line": "99"}, "connection": "china15tema2", "explain": null, "start_percent": 14.142, "width_percent": 5.068}, {"sql": "select * from `wallets` where `wallets`.`user_id` = ******** and `wallets`.`user_id` is not null and `active` = 1 limit 1", "type": "query", "params": [], "bindings": [********, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 101}, {"index": 22, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 66}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 426}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}], "start": **********.234657, "duration": 0.01055, "duration_str": "10.55ms", "memory": 0, "memory_str": null, "filename": "PlayFiverTrait.php:101", "source": {"index": 21, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FTraits%2FProviders%2FPlayFiverTrait.php&line=101", "ajax": false, "filename": "PlayFiverTrait.php", "line": "101"}, "connection": "china15tema2", "explain": null, "start_percent": 19.21, "width_percent": 14.728}, {"sql": "update `wallets` set `balance_withdrawal` = `balance_withdrawal` - 4, `wallets`.`updated_at` = '2024-11-28 17:06:37' where `id` = 279", "type": "query", "params": [], "bindings": ["2024-11-28 17:06:37", 279], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 116}, {"index": 18, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 66}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 426}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}], "start": **********.280345, "duration": 0.00544, "duration_str": "5.44ms", "memory": 0, "memory_str": null, "filename": "PlayFiverTrait.php:116", "source": {"index": 17, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 116}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FTraits%2FProviders%2FPlayFiverTrait.php&line=116", "ajax": false, "filename": "PlayFiverTrait.php", "line": "116"}, "connection": "china15tema2", "explain": null, "start_percent": 33.938, "width_percent": 7.595}, {"sql": "insert into `orders` (`user_id`, `session_id`, `transaction_id`, `game`, `game_uuid`, `type`, `type_money`, `amount`, `providers`, `refunded`, `round_id`, `status`, `updated_at`, `created_at`) values (********, '6748cd455d2fb', '1efadc44-1609-6d6c-b332-3e915b0bbbdf', '126', '126', 'bet', 'balance_withdrawal', 4, 'play_fiver', 0, '6748cd455d2fb', 1, '2024-11-28 17:06:37', '2024-11-28 17:06:37')", "type": "query", "params": [], "bindings": [********, "6748cd455d2fb", "1efadc44-1609-6d6c-b332-3e915b0bbbdf", "126", "126", "bet", "balance_withdrawal", 4, "play_fiver", 0, "6748cd455d2fb", 1, "2024-11-28 17:06:37", "2024-11-28 17:06:37"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 165}, {"index": 22, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 66}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 426}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}], "start": **********.328772, "duration": 0.004690000000000001, "duration_str": "4.69ms", "memory": 0, "memory_str": null, "filename": "PlayFiverTrait.php:165", "source": {"index": 21, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FTraits%2FProviders%2FPlayFiverTrait.php&line=165", "ajax": false, "filename": "PlayFiverTrait.php", "line": "165"}, "connection": "china15tema2", "explain": null, "start_percent": 41.533, "width_percent": 6.548}, {"sql": "select * from `users` where `users`.`id` = ******** limit 1", "type": "query", "params": [], "bindings": [********], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 667}, {"index": 21, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 179}, {"index": 22, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 66}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 426}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}], "start": **********.371568, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "Core.php:667", "source": {"index": 20, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 667}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHelpers%2FCore.php&line=667", "ajax": false, "filename": "Core.php", "line": "667"}, "connection": "china15tema2", "explain": null, "start_percent": 48.08, "width_percent": 3.155}, {"sql": "select * from `orders` where `transaction_id` = '1efadc44-1609-6d6c-b332-3e915b0bbbdf' and `type` = 'check' and `status` = 0 limit 1", "type": "query", "params": [], "bindings": ["1efadc44-1609-6d6c-b332-3e915b0bbbdf", "check", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 670}, {"index": 17, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 179}, {"index": 18, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 66}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 426}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}], "start": **********.4113781, "duration": 0.01119, "duration_str": "11.19ms", "memory": 0, "memory_str": null, "filename": "Core.php:670", "source": {"index": 16, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 670}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHelpers%2FCore.php&line=670", "ajax": false, "filename": "Core.php", "line": "670"}, "connection": "china15tema2", "explain": null, "start_percent": 51.236, "width_percent": 15.622}, {"sql": "select * from `wallets` where `user_id` = ******** limit 1", "type": "query", "params": [], "bindings": [********], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 216}, {"index": 17, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 691}, {"index": 18, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 179}, {"index": 19, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 66}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 426}], "start": **********.462812, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "Core.php:216", "source": {"index": 16, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHelpers%2FCore.php&line=216", "ajax": false, "filename": "Core.php", "line": "216"}, "connection": "china15tema2", "explain": null, "start_percent": 66.857, "width_percent": 6.492}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 217}, {"index": 20, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 691}, {"index": 21, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 179}, {"index": 22, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 66}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 426}], "start": **********.508055, "duration": 0.01859, "duration_str": "18.59ms", "memory": 0, "memory_str": null, "filename": "Core.php:217", "source": {"index": 19, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 217}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHelpers%2FCore.php&line=217", "ajax": false, "filename": "Core.php", "line": "217"}, "connection": "china15tema2", "explain": null, "start_percent": 73.349, "width_percent": 25.953}, {"sql": "update `wallets` set `balance_deposit_rollover` = 0, `wallets`.`updated_at` = '2024-11-28 17:06:37' where `id` = 279", "type": "query", "params": [], "bindings": [0, "2024-11-28 17:06:37", 279], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 326}, {"index": 16, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 691}, {"index": 17, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 179}, {"index": 18, "namespace": null, "name": "app/Traits/Providers/PlayFiverTrait.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Traits/Providers/PlayFiverTrait.php", "line": 66}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 426}], "start": **********.561201, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Core.php:326", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 326}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHelpers%2FCore.php&line=326", "ajax": false, "filename": "Core.php", "line": "326"}, "connection": "china15tema2", "explain": null, "start_percent": 99.302, "width_percent": 0.698}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Wallet": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "App\\Models\\GamesKey": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGamesKey.php&line=1", "ajax": false, "filename": "GamesKey.php", "line": "?"}}, "App\\Models\\Setting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cKO2lfCIGj9w1Hp2GR6npfXfiHrTs4vehmW9K2Pi", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/playfiver/webhook", "status_code": "<pre class=sf-dump id=sf-dump-2015577395 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2015577395\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1146872738 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1146872738\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1163002240 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">WinBet</span>\"\n  \"<span class=sf-dump-key>agent_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">dinhosilva</span>\"\n  \"<span class=sf-dump-key>agent_secret</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cb996222-8f9d-4788-bb95-9efb86bbe2db</span>\"\n  \"<span class=sf-dump-key>user_code</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>user_balance</span>\" => <span class=sf-dump-num>92</span>\n  \"<span class=sf-dump-key>game_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">slot</span>\"\n  \"<span class=sf-dump-key>slot</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>provider_code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">PGSOFT</span>\"\n    \"<span class=sf-dump-key>game_code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">126</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">BASE</span>\"\n    \"<span class=sf-dump-key>round_id</span>\" => \"<span class=sf-dump-str title=\"13 characters\">6748cd455d2fb</span>\"\n    \"<span class=sf-dump-key>bet</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>win</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>txn_id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">1efadc44-1609-6d6c-b332-3e915b0bbbdf</span>\"\n    \"<span class=sf-dump-key>txn_type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">debit_credit</span>\"\n    \"<span class=sf-dump-key>user_before_balance</span>\" => <span class=sf-dump-num>92</span>\n    \"<span class=sf-dump-key>user_after_balance</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2024-11-28</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163002240\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1429768728 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">GuzzleHttp/7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">429</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429768728\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-203916567 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-203916567\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1475989764 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 20:06:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkZYdnlLVkt3T1Fnb2JSQWYrVXhPZWc9PSIsInZhbHVlIjoiU3FkZ1VMaU9vUU5ld01aWFBUVTU0cnFtV2xjd3U4bEd5Rkx5dUZrSk1zZnBNUDZjSmxUWUZpaUtDd1hDLzJoSDQ1VUFLQlh1TFB6dkxhZHFpUU5nOW1SRWRKam1jN2p1UEtCcm4xMk5SeDVQREF1TjltWEV3VEFBWENmWU5Yd2oiLCJtYWMiOiJjZWViYTFiNjJmYTY3YzdmM2U5MWUzNzEwMmQ3NTBhOTZhY2IyZjJkZTYzNTc0Yzg3YWY1ZDE1YTY1NDBiNGUyIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 22:06:37 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6IjcxNDU1RCtNd1QwOG9MY0F3b0NLVlE9PSIsInZhbHVlIjoiQmtjRWZudjJIT2E5N3doeE1raTBoMEFKNXcyWTJMeDgrVGsycmZuc0hwR3dYTHdDTElKOU1kRXhOR1d4WU13TXVsK3FnZHVPTWtKZWVkLzBiZUlRQnB4Z21JRU0vWm9hWDgzYURnSlRRYlNWdTlqWmlFRVhZU0dzWWZISTNhVHkiLCJtYWMiOiI2MWNmNjc5YzE1ODY2OTgxYjE2NWVhYTRiNjE0NTkxYmY3MGZjODZmNjA0OTAzOWZkMmRjM2Y4MTk3ZmZiZGEzIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 22:06:37 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkZYdnlLVkt3T1Fnb2JSQWYrVXhPZWc9PSIsInZhbHVlIjoiU3FkZ1VMaU9vUU5ld01aWFBUVTU0cnFtV2xjd3U4bEd5Rkx5dUZrSk1zZnBNUDZjSmxUWUZpaUtDd1hDLzJoSDQ1VUFLQlh1TFB6dkxhZHFpUU5nOW1SRWRKam1jN2p1UEtCcm4xMk5SeDVQREF1TjltWEV3VEFBWENmWU5Yd2oiLCJtYWMiOiJjZWViYTFiNjJmYTY3YzdmM2U5MWUzNzEwMmQ3NTBhOTZhY2IyZjJkZTYzNTc0Yzg3YWY1ZDE1YTY1NDBiNGUyIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 22:06:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6IjcxNDU1RCtNd1QwOG9MY0F3b0NLVlE9PSIsInZhbHVlIjoiQmtjRWZudjJIT2E5N3doeE1raTBoMEFKNXcyWTJMeDgrVGsycmZuc0hwR3dYTHdDTElKOU1kRXhOR1d4WU13TXVsK3FnZHVPTWtKZWVkLzBiZUlRQnB4Z21JRU0vWm9hWDgzYURnSlRRYlNWdTlqWmlFRVhZU0dzWWZISTNhVHkiLCJtYWMiOiI2MWNmNjc5YzE1ODY2OTgxYjE2NWVhYTRiNjE0NTkxYmY3MGZjODZmNjA0OTAzOWZkMmRjM2Y4MTk3ZmZiZGEzIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 22:06:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1475989764\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1555340087 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cKO2lfCIGj9w1Hp2GR6npfXfiHrTs4vehmW9K2Pi</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555340087\", {\"maxDepth\":0})</script>\n"}}