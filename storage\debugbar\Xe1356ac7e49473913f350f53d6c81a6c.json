{"__meta": {"id": "Xe1356ac7e49473913f350f53d6c81a6c", "datetime": "2024-11-28 12:25:47", "utime": 1732807547.761638, "method": "GET", "uri": "/api/games/all", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732807532.462169, "end": 1732807547.761663, "duration": 15.29949402809143, "duration_str": "15.3s", "measures": [{"label": "Booting", "start": 1732807532.462169, "relative_start": 0, "end": 1732807536.885637, "relative_end": 1732807536.885637, "duration": 4.423468112945557, "duration_str": "4.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732807536.885651, "relative_start": 4.423482179641724, "end": 1732807547.761667, "relative_end": 4.0531158447265625e-06, "duration": 10.876015901565552, "duration_str": "10.88s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 15153368, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/games/all", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@index", "namespace": null, "prefix": "api/games", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=44\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:44-53</a>"}, "queries": {"nb_statements": 1023, "nb_visible_statements": 500, "nb_excluded_statements": 523, "nb_failed_statements": 0, "accumulated_duration": 1.1039199999999985, "accumulated_duration_str": "1.1s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `providers` where exists (select * from `games` where `providers`.`id` = `games`.`provider_id` and `show_home` = 1 and `status` = 1) and `status` = 1 order by `name` desc", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.059474, "duration": 0.01251, "duration_str": "12.51ms", "memory": 0, "memory_str": null, "filename": "GameController.php:50", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=50", "ajax": false, "filename": "GameController.php", "line": "50"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 1.133}, {"sql": "select * from `games` where `show_home` = 1 and `status` = 1 and `games`.`provider_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 11126) order by `views` desc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.173208, "duration": 0.02594, "duration_str": "25.94ms", "memory": 0, "memory_str": null, "filename": "GameController.php:50", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=50", "ajax": false, "filename": "GameController.php", "line": "50"}, "connection": "china15tema2", "explain": null, "start_percent": 1.133, "width_percent": 2.35}, {"sql": "select * from `providers` where `providers`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 11126)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.3113399, "duration": 0.01814, "duration_str": "18.14ms", "memory": 0, "memory_str": null, "filename": "GameController.php:50", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=50", "ajax": false, "filename": "GameController.php", "line": "50"}, "connection": "china15tema2", "explain": null, "start_percent": 3.483, "width_percent": 1.643}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 828 limit 1", "type": "query", "params": [], "bindings": [********, 828], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.515259, "duration": 0.00898, "duration_str": "8.98ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 5.126, "width_percent": 0.813}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 828 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [828], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.568125, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 5.94, "width_percent": 0.039}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 828 limit 1", "type": "query", "params": [], "bindings": [********, 828], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.572938, "duration": 0.01687, "duration_str": "16.87ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 5.979, "width_percent": 1.528}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 828 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [828], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.61342, "duration": 0.0141, "duration_str": "14.1ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 7.507, "width_percent": 1.277}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 841 limit 1", "type": "query", "params": [], "bindings": [********, 841], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.6525838, "duration": 0.01729, "duration_str": "17.29ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 8.784, "width_percent": 1.566}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 841 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [841], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.699566, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 10.35, "width_percent": 0.191}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 841 limit 1", "type": "query", "params": [], "bindings": [********, 841], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.733571, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 10.542, "width_percent": 0.041}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 841 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [841], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.7553911, "duration": 0.00645, "duration_str": "6.45ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 10.582, "width_percent": 0.584}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 824 limit 1", "type": "query", "params": [], "bindings": [********, 824], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.7902422, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 11.167, "width_percent": 0.106}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 824 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [824], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8405359, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 11.273, "width_percent": 0.048}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 824 limit 1", "type": "query", "params": [], "bindings": [********, 824], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8700268, "duration": 0.007809999999999999, "duration_str": "7.81ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 11.321, "width_percent": 0.707}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 824 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [824], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.9008372, "duration": 0.02117, "duration_str": "21.17ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 12.028, "width_percent": 1.918}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 844 limit 1", "type": "query", "params": [], "bindings": [********, 844], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.962007, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 13.946, "width_percent": 0.319}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 844 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [844], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.991259, "duration": 0.01473, "duration_str": "14.73ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 14.265, "width_percent": 1.334}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 844 limit 1", "type": "query", "params": [], "bindings": [********, 844], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.0381792, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 15.599, "width_percent": 0.042}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 844 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [844], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.0686371, "duration": 0.02063, "duration_str": "20.63ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 15.641, "width_percent": 1.869}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 851 limit 1", "type": "query", "params": [], "bindings": [********, 851], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.126827, "duration": 0.007980000000000001, "duration_str": "7.98ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 17.509, "width_percent": 0.723}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 851 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [851], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.174758, "duration": 0.00739, "duration_str": "7.39ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 18.232, "width_percent": 0.669}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 851 limit 1", "type": "query", "params": [], "bindings": [********, 851], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.2143419, "duration": 0.005849999999999999, "duration_str": "5.85ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 18.902, "width_percent": 0.53}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 851 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [851], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.240671, "duration": 0.02131, "duration_str": "21.31ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 19.432, "width_percent": 1.93}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 830 limit 1", "type": "query", "params": [], "bindings": [********, 830], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.298073, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 21.362, "width_percent": 0.051}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 830 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [830], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.33927, "duration": 0.01737, "duration_str": "17.37ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 21.413, "width_percent": 1.573}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 830 limit 1", "type": "query", "params": [], "bindings": [********, 830], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.382704, "duration": 0.00708, "duration_str": "7.08ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 22.986, "width_percent": 0.641}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 830 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [830], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4191241, "duration": 0.00877, "duration_str": "8.77ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 23.628, "width_percent": 0.794}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 831 limit 1", "type": "query", "params": [], "bindings": [********, 831], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4553661, "duration": 0.0126, "duration_str": "12.6ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 24.422, "width_percent": 1.141}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 831 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [831], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.5005732, "duration": 0.011009999999999999, "duration_str": "11.01ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 25.563, "width_percent": 0.997}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 831 limit 1", "type": "query", "params": [], "bindings": [********, 831], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.5449111, "duration": 0.01384, "duration_str": "13.84ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 26.561, "width_percent": 1.254}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 831 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [831], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.588083, "duration": 0.021920000000000002, "duration_str": "21.92ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 27.815, "width_percent": 1.986}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1195 limit 1", "type": "query", "params": [], "bindings": [********, 1195], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.636525, "duration": 0.013349999999999999, "duration_str": "13.35ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 29.8, "width_percent": 1.209}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1195 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.683106, "duration": 0.01492, "duration_str": "14.92ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 31.009, "width_percent": 1.352}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1195 limit 1", "type": "query", "params": [], "bindings": [********, 1195], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.725416, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 32.361, "width_percent": 0.369}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1195 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.76579, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 32.73, "width_percent": 0.045}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 397 limit 1", "type": "query", "params": [], "bindings": [********, 397], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.798817, "duration": 0.00707, "duration_str": "7.07ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 32.775, "width_percent": 0.64}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 397 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [397], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.839488, "duration": 0.010490000000000001, "duration_str": "10.49ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 33.415, "width_percent": 0.95}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 397 limit 1", "type": "query", "params": [], "bindings": [********, 397], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.878247, "duration": 0.00626, "duration_str": "6.26ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 34.366, "width_percent": 0.567}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 397 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [397], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.9183092, "duration": 0.01133, "duration_str": "11.33ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 34.933, "width_percent": 1.026}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1194 limit 1", "type": "query", "params": [], "bindings": [********, 1194], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.954704, "duration": 0.00734, "duration_str": "7.34ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 35.959, "width_percent": 0.665}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1194 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1194], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.992634, "duration": 0.01088, "duration_str": "10.88ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 36.624, "width_percent": 0.986}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1194 limit 1", "type": "query", "params": [], "bindings": [********, 1194], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.0204601, "duration": 0.01871, "duration_str": "18.71ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 37.61, "width_percent": 1.695}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1194 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1194], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.0778, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 39.304, "width_percent": 0.22}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1190 limit 1", "type": "query", "params": [], "bindings": [********, 1190], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.113754, "duration": 0.00824, "duration_str": "8.24ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 39.525, "width_percent": 0.746}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1190 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1190], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.14617, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 40.271, "width_percent": 0.306}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1190 limit 1", "type": "query", "params": [], "bindings": [********, 1190], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.178333, "duration": 0.0065, "duration_str": "6.5ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 40.577, "width_percent": 0.589}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1190 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1190], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.2109728, "duration": 0.00657, "duration_str": "6.57ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 41.166, "width_percent": 0.595}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1193 limit 1", "type": "query", "params": [], "bindings": [********, 1193], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.240633, "duration": 0.02124, "duration_str": "21.24ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 41.761, "width_percent": 1.924}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1193 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1193], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.292256, "duration": 0.00521, "duration_str": "5.21ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 43.685, "width_percent": 0.472}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1193 limit 1", "type": "query", "params": [], "bindings": [********, 1193], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.326253, "duration": 0.00718, "duration_str": "7.18ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 44.157, "width_percent": 0.65}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1193 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1193], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.358468, "duration": 0.00735, "duration_str": "7.35ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 44.808, "width_percent": 0.666}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1192 limit 1", "type": "query", "params": [], "bindings": [********, 1192], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.390055, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 45.473, "width_percent": 0.171}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1192 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1192], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4312718, "duration": 0.01289, "duration_str": "12.89ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 45.645, "width_percent": 1.168}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1192 limit 1", "type": "query", "params": [], "bindings": [********, 1192], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.456485, "duration": 0.01214, "duration_str": "12.14ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 46.812, "width_percent": 1.1}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1192 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1192], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.506437, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 47.912, "width_percent": 0.302}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1191 limit 1", "type": "query", "params": [], "bindings": [********, 1191], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.531436, "duration": 0.0128, "duration_str": "12.8ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 48.214, "width_percent": 1.16}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1191 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1191], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.574056, "duration": 0.00584, "duration_str": "5.84ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 49.373, "width_percent": 0.529}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1191 limit 1", "type": "query", "params": [], "bindings": [********, 1191], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.613594, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 49.902, "width_percent": 0.048}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1191 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1191], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.6404638, "duration": 0.00902, "duration_str": "9.02ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 49.95, "width_percent": 0.817}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1060 limit 1", "type": "query", "params": [], "bindings": [********, 1060], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.6803548, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 50.767, "width_percent": 0.07}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1060 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1060], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.7143579, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 50.837, "width_percent": 0.307}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1060 limit 1", "type": "query", "params": [], "bindings": [********, 1060], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.745994, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 51.144, "width_percent": 0.345}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1060 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1060], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.785514, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 51.489, "width_percent": 0.041}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1066 limit 1", "type": "query", "params": [], "bindings": [********, 1066], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.811898, "duration": 0.010119999999999999, "duration_str": "10.12ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 51.53, "width_percent": 0.917}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1066 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1066], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.848982, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 52.447, "width_percent": 0.16}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1066 limit 1", "type": "query", "params": [], "bindings": [********, 1066], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8672101, "duration": 0.01063, "duration_str": "10.63ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 52.607, "width_percent": 0.963}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1066 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1066], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.904999, "duration": 0.006900000000000001, "duration_str": "6.9ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 53.57, "width_percent": 0.625}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1059 limit 1", "type": "query", "params": [], "bindings": [********, 1059], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.945543, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 54.195, "width_percent": 0.046}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1059 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1059], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.971209, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 54.241, "width_percent": 0.042}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1059 limit 1", "type": "query", "params": [], "bindings": [********, 1059], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.98985, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 54.283, "width_percent": 0.231}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1059 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1059], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.0133379, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 54.514, "width_percent": 0.405}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1062 limit 1", "type": "query", "params": [], "bindings": [********, 1062], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.034883, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 54.919, "width_percent": 0.273}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1062 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1062], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.060673, "duration": 0.00919, "duration_str": "9.19ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 55.191, "width_percent": 0.832}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1062 limit 1", "type": "query", "params": [], "bindings": [********, 1062], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.085848, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 56.024, "width_percent": 0.333}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1062 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1062], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.118151, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 56.357, "width_percent": 0.05}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1065 limit 1", "type": "query", "params": [], "bindings": [********, 1065], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.1283748, "duration": 0.01357, "duration_str": "13.57ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 56.407, "width_percent": 1.229}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1065 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1065], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.152743, "duration": 0.01348, "duration_str": "13.48ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 57.636, "width_percent": 1.221}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1065 limit 1", "type": "query", "params": [], "bindings": [********, 1065], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.185986, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 58.858, "width_percent": 0.348}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1065 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1065], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.2123752, "duration": 0.00327, "duration_str": "3.27ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 59.205, "width_percent": 0.296}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1075 limit 1", "type": "query", "params": [], "bindings": [********, 1075], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.233418, "duration": 0.00862, "duration_str": "8.62ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 59.502, "width_percent": 0.781}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1075 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1075], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.261704, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 60.282, "width_percent": 0.045}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1075 limit 1", "type": "query", "params": [], "bindings": [********, 1075], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.2711282, "duration": 0.00504, "duration_str": "5.04ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 60.328, "width_percent": 0.457}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1075 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1075], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.2944531, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 60.784, "width_percent": 0.044}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1076 limit 1", "type": "query", "params": [], "bindings": [********, 1076], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.310205, "duration": 0.0063, "duration_str": "6.3ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 60.829, "width_percent": 0.571}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1076 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1076], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.330272, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 61.399, "width_percent": 0.053}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1076 limit 1", "type": "query", "params": [], "bindings": [********, 1076], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.343862, "duration": 0.00609, "duration_str": "6.09ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 61.452, "width_percent": 0.552}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1076 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1076], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.364657, "duration": 0.009179999999999999, "duration_str": "9.18ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 62.004, "width_percent": 0.832}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 811 limit 1", "type": "query", "params": [], "bindings": [********, 811], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.383833, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 62.835, "width_percent": 0.287}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 811 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [811], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.40009, "duration": 0.009810000000000001, "duration_str": "9.81ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 63.122, "width_percent": 0.889}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 811 limit 1", "type": "query", "params": [], "bindings": [********, 811], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.425833, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 64.011, "width_percent": 0.16}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 811 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [811], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.445556, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 64.171, "width_percent": 0.224}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 806 limit 1", "type": "query", "params": [], "bindings": [********, 806], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.453407, "duration": 0.01259, "duration_str": "12.59ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 64.395, "width_percent": 1.14}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 806 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [806], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.485338, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 65.536, "width_percent": 0.273}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 806 limit 1", "type": "query", "params": [], "bindings": [********, 806], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.502278, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 65.808, "width_percent": 0.042}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 806 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [806], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.521682, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 65.85, "width_percent": 0.049}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 807 limit 1", "type": "query", "params": [], "bindings": [********, 807], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.5397, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 65.899, "width_percent": 0.044}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 807 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [807], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.557744, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 65.943, "width_percent": 0.041}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 807 limit 1", "type": "query", "params": [], "bindings": [********, 807], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.569969, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 65.984, "width_percent": 0.038}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 807 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [807], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.589162, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 66.022, "width_percent": 0.048}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 766 limit 1", "type": "query", "params": [], "bindings": [********, 766], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.6015232, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 66.07, "width_percent": 0.041}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.615663, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.111, "width_percent": 0.046}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.616343, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.157, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.616749, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.181, "width_percent": 0.025}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6177921, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.207, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6182458, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.234, "width_percent": 0.022}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.618608, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.256, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.619017, "duration": 0.00658, "duration_str": "6.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.275, "width_percent": 0.596}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.626479, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.871, "width_percent": 0.037}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.627099, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.908, "width_percent": 0.024}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.627513, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.932, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.627907, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.955, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6287282, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.979, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.629176, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.007, "width_percent": 0.036}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6297212, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.043, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.630149, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.069, "width_percent": 0.022}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.630936, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.091, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.631331, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.114, "width_percent": 0.028}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.631768, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.142, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.632151, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.164, "width_percent": 0.02}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.633103, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.184, "width_percent": 0.02}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.633478, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.204, "width_percent": 0.063}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.63429, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.267, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6346228, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.286, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.635278, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.307, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6356862, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.333, "width_percent": 0.091}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6368098, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.424, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.637171, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.446, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.637912, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.463, "width_percent": 0.149}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.639682, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.612, "width_percent": 0.024}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.640069, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.636, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.64039, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.655, "width_percent": 0.019}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6410189, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.674, "width_percent": 0.049}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.641717, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.723, "width_percent": 0.267}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.644784, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.99, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6451669, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.014, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.64584, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.038, "width_percent": 0.02}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.646201, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.057, "width_percent": 0.021}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.646544, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.078, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.646882, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.096, "width_percent": 0.025}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6478572, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.122, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6482909, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.148, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.64868, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.171, "width_percent": 0.083}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.649744, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.254, "width_percent": 0.02}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.650609, "duration": 0.01123, "duration_str": "11.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.274, "width_percent": 1.017}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662086, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.291, "width_percent": 0.022}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662473, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.313, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.66284, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.336, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.663652, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.356, "width_percent": 0.173}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6657422, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.529, "width_percent": 0.028}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.666182, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.558, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6666, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.582, "width_percent": 0.025}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6676311, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.607, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.668086, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.632, "width_percent": 0.147}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6698492, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.779, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.670275, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.804, "width_percent": 0.023}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.67113, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.827, "width_percent": 0.226}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.673755, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.052, "width_percent": 0.019}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674074, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.071, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674392, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.088, "width_percent": 0.284}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.678324, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.372, "width_percent": 0.14}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68007, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.512, "width_percent": 0.02}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.680404, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.532, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.680722, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.549, "width_percent": 0.094}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6822839, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.644, "width_percent": 0.308}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.685875, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.952, "width_percent": 0.037}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6864412, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.989, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.686844, "duration": 0.00276, "duration_str": "2.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.014, "width_percent": 0.25}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6903179, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.264, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6907048, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.286, "width_percent": 0.021}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6910448, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.307, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691428, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.329, "width_percent": 0.023}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6923308, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.352, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.69273, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.375, "width_percent": 0.083}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.693792, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.458, "width_percent": 0.111}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.695184, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.569, "width_percent": 0.206}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.698028, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.774, "width_percent": 0.192}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7003028, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.966, "width_percent": 0.022}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.700674, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.988, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.701043, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.007, "width_percent": 0.022}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.701855, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.029, "width_percent": 0.332}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7057211, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.361, "width_percent": 0.183}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.707912, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.544, "width_percent": 0.072}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7088628, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.616, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.710417, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.639, "width_percent": 0.297}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.71388, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.936, "width_percent": 0.026}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.714311, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.963, "width_percent": 0.02}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.714654, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.983, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.715536, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.003, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.716011, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.032, "width_percent": 0.13}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.71759, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.162, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7180579, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.188, "width_percent": 0.023}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7189329, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.211, "width_percent": 0.145}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7206929, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.356, "width_percent": 0.024}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.721098, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.379, "width_percent": 0.048}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.721789, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.427, "width_percent": 0.353}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.72949, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.781, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.73001, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.811, "width_percent": 0.041}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7306142, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.851, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7310379, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.877, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.731725, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.895, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.732112, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.918, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7324169, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.937, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.732744, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.955, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7377028, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.972, "width_percent": 0.262}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.740746, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.234, "width_percent": 0.021}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.741158, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.254, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.741556, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.277, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.742434, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.298, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7428339, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.322, "width_percent": 0.021}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7431788, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.343, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7435539, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.367, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.744239, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.385, "width_percent": 0.063}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.745072, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.447, "width_percent": 0.048}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.745734, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.495, "width_percent": 0.342}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7496622, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.837, "width_percent": 0.026}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.750844, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.863, "width_percent": 0.252}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.753808, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.115, "width_percent": 0.025}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7542439, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.14, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7546508, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.164, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7556682, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.185, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7561522, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.213, "width_percent": 0.028}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7566361, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.241, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7570899, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.267, "width_percent": 0.052}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7583308, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.319, "width_percent": 0.306}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.761899, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.625, "width_percent": 0.328}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765714, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.953, "width_percent": 0.316}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.769468, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.269, "width_percent": 0.03}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.771185, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.299, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7716181, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.323, "width_percent": 0.187}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.77382, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.509, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.774195, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.53, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.774921, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.551, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.775295, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.573, "width_percent": 0.019}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.775617, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.592, "width_percent": 0.165}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.777577, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.756, "width_percent": 0.226}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.780587, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.982, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.780999, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.005, "width_percent": 0.022}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.781368, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.026, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7817929, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.053, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.782631, "duration": 0.00307, "duration_str": "3.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.076, "width_percent": 0.278}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7858791, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.354, "width_percent": 0.106}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.787201, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.46, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7875788, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.481, "width_percent": 0.02}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7884312, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.501, "width_percent": 0.108}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7897608, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.609, "width_percent": 0.024}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.790147, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.632, "width_percent": 0.075}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7911248, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.708, "width_percent": 0.023}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7954931, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.73, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.795914, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.757, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.796314, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.779, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7967858, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.806, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.801076, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.83, "width_percent": 0.051}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.80179, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.881, "width_percent": 0.172}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.803823, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.053, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8042212, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.074, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8050668, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.099, "width_percent": 0.055}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.805866, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.154, "width_percent": 0.349}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.809914, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.503, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8103511, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.527, "width_percent": 0.028}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.811398, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.556, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.811827, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.581, "width_percent": 0.164}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.813766, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.745, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.814181, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.769, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8150542, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.794, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8154662, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.818, "width_percent": 0.183}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.817608, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.001, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.818047, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.026, "width_percent": 0.273}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.821913, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.298, "width_percent": 0.034}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.822473, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.332, "width_percent": 0.026}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8229282, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.358, "width_percent": 0.248}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.825845, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.606, "width_percent": 0.028}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.826972, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.634, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.827449, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.662, "width_percent": 0.264}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8305159, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.925, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.830909, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.947, "width_percent": 0.025}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8318398, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.972, "width_percent": 0.166}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.833853, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.138, "width_percent": 0.026}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8342838, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.164, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.834672, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.186, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.835517, "duration": 0.006030000000000001, "duration_str": "6.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.207, "width_percent": 0.546}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.841783, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.753, "width_percent": 0.029}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8422668, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.782, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.842706, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.806, "width_percent": 0.023}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.84365, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.828, "width_percent": 0.128}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.845231, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.956, "width_percent": 0.027}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.845681, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.983, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8462398, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.019, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8471742, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.042, "width_percent": 0.154}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.849077, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.196, "width_percent": 0.034}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8495991, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.23, "width_percent": 0.171}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.851664, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.401, "width_percent": 0.168}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8543081, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.568, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.854762, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.595, "width_percent": 0.026}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.855206, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.621, "width_percent": 0.221}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.857827, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.842, "width_percent": 0.346}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.862447, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.188, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.862925, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.214, "width_percent": 0.041}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.863531, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.255, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.863968, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.28, "width_percent": 0.023}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.864865, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.302, "width_percent": 0.392}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8694952, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.694, "width_percent": 0.026}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.870402, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.721, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8708692, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.747, "width_percent": 0.059}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.872287, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.806, "width_percent": 0.489}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.877892, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.295, "width_percent": 0.027}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.878372, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.322, "width_percent": 0.289}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8817601, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.611, "width_percent": 0.043}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.882993, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.655, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8834448, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.68, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8838491, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.703, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.884281, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.727, "width_percent": 0.023}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8851922, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.75, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.885658, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.777, "width_percent": 0.051}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8863702, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.828, "width_percent": 0.28}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.889636, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.108, "width_percent": 0.2}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.892679, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.308, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8931541, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.335, "width_percent": 0.026}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.893594, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.361, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.894034, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.386, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.89496, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.409, "width_percent": 0.243}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8978682, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.652, "width_percent": 0.347}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.901878, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.999, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9023378, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.025, "width_percent": 0.023}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.903288, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.048, "width_percent": 0.095}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9045038, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.143, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.904908, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.166, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.905367, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.189, "width_percent": 0.369}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.910155, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.558, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9106138, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.583, "width_percent": 0.024}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.911049, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.607, "width_percent": 0.068}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.911992, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.675, "width_percent": 0.025}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.912911, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.7, "width_percent": 0.307}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9164739, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.007, "width_percent": 0.022}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9168658, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.029, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.917259, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.052, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.91833, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.075, "width_percent": 0.303}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9219072, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.379, "width_percent": 0.025}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.922342, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.404, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9228022, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.429, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.923709, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.454, "width_percent": 0.177}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.925811, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.63, "width_percent": 0.025}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9262578, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.656, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9266782, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.677, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927637, "duration": 0.00457, "duration_str": "4.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.701, "width_percent": 0.414}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932389, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.115, "width_percent": 0.026}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932861, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.141, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.933316, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.168, "width_percent": 0.03}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.934395, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.197, "width_percent": 0.297}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9378831, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.495, "width_percent": 0.027}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9383311, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.522, "width_percent": 0.301}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.947391, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.822, "width_percent": 0.04}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9486382, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.862, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.949108, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.889, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.949543, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.911, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.949941, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.933, "width_percent": 0.396}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.954873, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.329, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9553802, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.358, "width_percent": 0.2}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957768, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.558, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9582329, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.585, "width_percent": 0.02}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959008, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.605, "width_percent": 0.02}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959359, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.625, "width_percent": 0.02}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959696, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.645, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960031, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.663, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960709, "duration": 0.00492, "duration_str": "4.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.681, "width_percent": 0.446}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.965787, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.127, "width_percent": 0.172}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9678612, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.299, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.968331, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.326, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9692721, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.351, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.969696, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.377, "width_percent": 0.354}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9737449, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.731, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9740689, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.749, "width_percent": 0.022}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9747689, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.771, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.975159, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.793, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.975477, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.811, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.975826, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.832, "width_percent": 0.019}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.976578, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.851, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9769669, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.872, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977332, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.894, "width_percent": 0.377}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.981628, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.271, "width_percent": 0.02}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9823248, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.291, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.982674, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.312, "width_percent": 0.019}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9830089, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.331, "width_percent": 0.241}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.985789, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.572, "width_percent": 0.019}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.986504, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.591, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.986832, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.609, "width_percent": 0.02}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9871778, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.629, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.98749, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.647, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.988198, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.668, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.98852, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.687, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.98881, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.704, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.98913, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.722, "width_percent": 0.043}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.99024, "duration": 0.00556, "duration_str": "5.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.766, "width_percent": 0.504}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9959939, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.269, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.996422, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.292, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.996892, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.32, "width_percent": 0.026}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.998044, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.346, "width_percent": 0.326}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.001826, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.672, "width_percent": 0.031}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0023332, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.703, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.002787, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.729, "width_percent": 0.028}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.003797, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.758, "width_percent": 0.168}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0058272, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.926, "width_percent": 0.024}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0062559, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.951, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.00668, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.973, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.007557, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.998, "width_percent": 0.189}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.009809, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.187, "width_percent": 0.347}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.013782, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.534, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0141459, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.553, "width_percent": 0.022}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0148711, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.575, "width_percent": 0.375}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.01916, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.95, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.019478, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.967, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.019799, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.984, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.020674, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.008, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0211668, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.035, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.021567, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.057, "width_percent": 0.253}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0245178, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.31, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.025264, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.334, "width_percent": 0.352}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0293121, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.686, "width_percent": 0.026}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.029765, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.712, "width_percent": 0.037}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.030328, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.75, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.031345, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.774, "width_percent": 0.209}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.03382, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.983, "width_percent": 0.022}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.034201, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.005, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.034626, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.029, "width_percent": 0.02}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.035485, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.048, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0359209, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.074, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.036302, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.096, "width_percent": 0.263}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.039345, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.359, "width_percent": 0.207}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0422091, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.567, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.042656, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.594, "width_percent": 0.019}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.042995, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.613, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.043392, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.635, "width_percent": 0.022}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.044248, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.656, "width_percent": 0.126}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0458212, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.782, "width_percent": 0.089}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.046951, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.871, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.047535, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.896, "width_percent": 0.022}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0499132, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.918, "width_percent": 0.345}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0538979, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.263, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.054304, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.286, "width_percent": 0.303}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.057858, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.588, "width_percent": 0.025}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.058905, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.614, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.059335, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.636, "width_percent": 0.031}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0598218, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.667, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0602422, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.691, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.061177, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.715, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.061542, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.736, "width_percent": 0.37}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0658, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.106, "width_percent": 0.34}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.069747, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.445, "width_percent": 0.026}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.070711, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.472, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0711, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.492, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.07141, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.51, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.071774, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.531, "width_percent": 0.019}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.072453, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.55, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.072805, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.569, "width_percent": 0.021}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.073149, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.589, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0734959, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.607, "width_percent": 0.019}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0742161, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.626, "width_percent": 0.094}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.075382, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.72, "width_percent": 0.199}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.077725, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.919, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.078079, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.94, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.078748, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.961, "width_percent": 0.243}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0815542, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.204, "width_percent": 0.027}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.081982, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.231, "width_percent": 0.032}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0824568, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.262, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0855339, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.286, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.085917, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.31, "width_percent": 0.02}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.086247, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.33, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.086612, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.352, "width_percent": 0.02}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.087315, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.372, "width_percent": 0.207}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.089757, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.58, "width_percent": 0.023}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.090126, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.602, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.09044, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.62, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.091155, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.641, "width_percent": 0.226}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0937688, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.867, "width_percent": 0.022}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.094116, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.888, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0944269, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.906, "width_percent": 0.13}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0963778, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.035, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.096764, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.056, "width_percent": 0.024}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.0992699, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.08, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.099618, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.099, "width_percent": 0.019}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.100308, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.119, "width_percent": 0.115}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.101696, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.234, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.102017, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.251, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.102351, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.27, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.102986, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.287, "width_percent": 0.188}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.1051888, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.475, "width_percent": 0.384}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.1095839, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.86, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.1098979, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.878, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.110592, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.896, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.110967, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.917, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.1112719, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.935, "width_percent": 0.211}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.113743, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.146, "width_percent": 0.019}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.114405, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.165, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.114745, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.184, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.115053, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.202, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.1153738, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.219, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.116051, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.237, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.116428, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.26, "width_percent": 0.111}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.117783, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.37, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.11823, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.397, "width_percent": 0.024}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732807547.119361, "duration": 0.00639, "duration_str": "6.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.421, "width_percent": 0.579}, {"sql": "... 523 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"App\\Models\\Game": {"value": 255, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=1", "ajax": false, "filename": "Game.php", "line": "?"}}, "App\\Models\\Provider": {"value": 36, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FProvider.php&line=1", "ajax": false, "filename": "Provider.php", "line": "?"}}}, "count": 291, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/games/all", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-296215366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-296215366\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IkdibkJzV0hqcU1VMHNUME9aS0pvQVE9PSIsInZhbHVlIjoiTHhTVDV3YVU5Q2VXM0lDSzNQRGtTaGdBekVSUU1obHFmNzJJcmdmcHUzK1BQNlgyUjB6N0tOU0RBY3d5eFpncEFlUWZyTm5iUnJ0RUh4S09QcWZXRnFBS2N5b1dZZm5JS3kzRmdRaVd6Wkl4OFNDK1k4WnFVSmpWdFRFQitUN08iLCJtYWMiOiI1NGJiYjA5MTk2NzNiZDE3OTg0Mzc1ZDVmZmUyZDc2MjU1Njg3YjU3NTRlN2E4NWViNDAxYjc1ZjdmYTM3Nzc3IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6Im1xSmh5d1pFUm10eHRvdjFoaStRbWc9PSIsInZhbHVlIjoib3RqVW4wS3VGKy8zcVdkalVGRWZrbzcxT0EzRmFkTFZ4WnFvZkxpWkJSajV4OW4wRnlRVkRKQURPdG9RNzVmQUs4RnFPUW92WlpzbkNmWkkvTmxySm9jUnZjMmhidVEyQ0Q1dDh1VFRWWk8wc2kyQlh6VU44bkJrdktKb0RuZjkiLCJtYWMiOiJlZDMxODQ2Mjg3YWJlNTAzZDM1OTg1YTNkMGMzZTkwMTM3N2I4YTFkODA5NmQ5OWYzZWEwYmUwMzBhMzZiMWZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdibkJzV0hqcU1VMHNUME9aS0pvQVE9PSIsInZhbHVlIjoiTHhTVDV3YVU5Q2VXM0lDSzNQRGtTaGdBekVSUU1obHFmNzJJcmdmcHUzK1BQNlgyUjB6N0tOU0RBY3d5eFpncEFlUWZyTm5iUnJ0RUh4S09QcWZXRnFBS2N5b1dZZm5JS3kzRmdRaVd6Wkl4OFNDK1k4WnFVSmpWdFRFQitUN08iLCJtYWMiOiI1NGJiYjA5MTk2NzNiZDE3OTg0Mzc1ZDVmZmUyZDc2MjU1Njg3YjU3NTRlN2E4NWViNDAxYjc1ZjdmYTM3Nzc3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZE0YVLZQjR9JyraSrLfdfqjJhqLh1JxweQcNpzyV</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-393281597 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdibkJzV0hqcU1VMHNUME9aS0pvQVE9PSIsInZhbHVlIjoiTHhTVDV3YVU5Q2VXM0lDSzNQRGtTaGdBekVSUU1obHFmNzJJcmdmcHUzK1BQNlgyUjB6N0tOU0RBY3d5eFpncEFlUWZyTm5iUnJ0RUh4S09QcWZXRnFBS2N5b1dZZm5JS3kzRmdRaVd6Wkl4OFNDK1k4WnFVSmpWdFRFQitUN08iLCJtYWMiOiI1NGJiYjA5MTk2NzNiZDE3OTg0Mzc1ZDVmZmUyZDc2MjU1Njg3YjU3NTRlN2E4NWViNDAxYjc1ZjdmYTM3Nzc3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im1xSmh5d1pFUm10eHRvdjFoaStRbWc9PSIsInZhbHVlIjoib3RqVW4wS3VGKy8zcVdkalVGRWZrbzcxT0EzRmFkTFZ4WnFvZkxpWkJSajV4OW4wRnlRVkRKQURPdG9RNzVmQUs4RnFPUW92WlpzbkNmWkkvTmxySm9jUnZjMmhidVEyQ0Q1dDh1VFRWWk8wc2kyQlh6VU44bkJrdktKb0RuZjkiLCJtYWMiOiJlZDMxODQ2Mjg3YWJlNTAzZDM1OTg1YTNkMGMzZTkwMTM3N2I4YTFkODA5NmQ5OWYzZWEwYmUwMzBhMzZiMWZmIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-393281597\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-574243095 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 15:25:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574243095\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-71259126 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-71259126\", {\"maxDepth\":0})</script>\n"}}