{"__meta": {"id": "Xc589eee9b0cb3df8e76e701d6c571bc4", "datetime": "2024-11-28 11:29:28", "utime": **********.343399, "method": "GET", "uri": "/api/auth/verify", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804162.769254, "end": **********.343415, "duration": 5.574161052703857, "duration_str": "5.57s", "measures": [{"label": "Booting", "start": 1732804162.769254, "relative_start": 0, "end": 1732804163.689341, "relative_end": 1732804163.689341, "duration": 0.9200870990753174, "duration_str": "920ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732804163.689358, "relative_start": 0.9201040267944336, "end": **********.343417, "relative_end": 1.9073486328125e-06, "duration": 4.654058933258057, "duration_str": "4.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14284456, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/auth/verify", "middleware": "api, auth.jwt", "controller": "App\\Http\\Controllers\\Api\\Auth\\AuthController@verify", "as": "auth.", "namespace": null, "prefix": "api/auth", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FAuth%2FAuthController.php&line=146\" onclick=\"\">app/Http/Controllers/Api/Auth/AuthController.php:146-149</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0012900000000000001, "accumulated_duration_str": "1.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `likes` where `likes`.`user_id` = 17484654 and `likes`.`user_id` is not null", "type": "query", "params": [], "bindings": [17484654], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/User.php", "line": 181}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 81}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}], "start": **********.293987, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "User.php:181", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/User.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FUser.php&line=181", "ajax": false, "filename": "User.php", "line": "181"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/auth/verify", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1747661299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1747661299\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1878640052 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1878640052\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1082573033 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"758 characters\">_fbp=fb.1.1732723263756.71770186546337922; XSRF-TOKEN=eyJpdiI6IjdDclNIQkNmVnN3YnFqMFMyQzBtQmc9PSIsInZhbHVlIjoiSmNEMzg4MkFuKzQzRlV3TmR1eU1iWDl6cUhYTTVuREw5ekRNcitOVWtZd3hIK1B1dktuT3BROE1nTjkxZERhTnB2N2VhUS8wK0hvaWEycDV0ejRqaXAvR052RXl1d0ZDTGlDMFJRVEl3T1BSTWtGOE51MThDN2RTNGVib1F5NWciLCJtYWMiOiJjZDFmMTA2NzY5OWM3ZjJlNGNhOGMyOGZlMWU3MGVhZTI2ZGZlYjBjNTVlZTA2MGQzMjIyMzIxNzBhYzUyZWIyIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6InQxYitmSzVHS0tXby9zSXlXNjlsQ1E9PSIsInZhbHVlIjoiRkF4aGd6N08zaFU4UUhPY1dLakphOUhZaGJ4YkI5STVNV3NhNUMxYnBZQjVnNUZwRXhtV2lnMDlLUi9oZ0Y5UUFzWFFhdXA0MXExRnRlY0c3WVRheHhQeFIyeEsvaTdXa0d6am1hdzV5TnlYQ2hrK0RhdVQrSnlIOFZScXFLWk8iLCJtYWMiOiJmMzI2MzJlNDA5NWY3M2RmZTBhZjVkMzQ1YTEwZmQyOWNlNjE0ZTFiMzI0ZThmZGVjNzBjMTlhMDU5NDllM2FiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://job.forradapg.com/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjdDclNIQkNmVnN3YnFqMFMyQzBtQmc9PSIsInZhbHVlIjoiSmNEMzg4MkFuKzQzRlV3TmR1eU1iWDl6cUhYTTVuREw5ekRNcitOVWtZd3hIK1B1dktuT3BROE1nTjkxZERhTnB2N2VhUS8wK0hvaWEycDV0ejRqaXAvR052RXl1d0ZDTGlDMFJRVEl3T1BSTWtGOE51MThDN2RTNGVib1F5NWciLCJtYWMiOiJjZDFmMTA2NzY5OWM3ZjJlNGNhOGMyOGZlMWU3MGVhZTI2ZGZlYjBjNTVlZTA2MGQzMjIyMzIxNzBhYzUyZWIyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082573033\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_fbp</span>\" => \"<span class=sf-dump-str title=\"36 characters\">fb.1.1732723263756.71770186546337922</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjdDclNIQkNmVnN3YnFqMFMyQzBtQmc9PSIsInZhbHVlIjoiSmNEMzg4MkFuKzQzRlV3TmR1eU1iWDl6cUhYTTVuREw5ekRNcitOVWtZd3hIK1B1dktuT3BROE1nTjkxZERhTnB2N2VhUS8wK0hvaWEycDV0ejRqaXAvR052RXl1d0ZDTGlDMFJRVEl3T1BSTWtGOE51MThDN2RTNGVib1F5NWciLCJtYWMiOiJjZDFmMTA2NzY5OWM3ZjJlNGNhOGMyOGZlMWU3MGVhZTI2ZGZlYjBjNTVlZTA2MGQzMjIyMzIxNzBhYzUyZWIyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InQxYitmSzVHS0tXby9zSXlXNjlsQ1E9PSIsInZhbHVlIjoiRkF4aGd6N08zaFU4UUhPY1dLakphOUhZaGJ4YkI5STVNV3NhNUMxYnBZQjVnNUZwRXhtV2lnMDlLUi9oZ0Y5UUFzWFFhdXA0MXExRnRlY0c3WVRheHhQeFIyeEsvaTdXa0d6am1hdzV5TnlYQ2hrK0RhdVQrSnlIOFZScXFLWk8iLCJtYWMiOiJmMzI2MzJlNDA5NWY3M2RmZTBhZjVkMzQ1YTEwZmQyOWNlNjE0ZTFiMzI0ZThmZGVjNzBjMTlhMDU5NDllM2FiIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1397369725 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:29:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1397369725\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1986166693 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1986166693\", {\"maxDepth\":0})</script>\n"}}