{"__meta": {"id": "Xc809854aacdc2f2aec67190110440754", "datetime": "2024-11-28 11:28:59", "utime": 1732804139.788782, "method": "GET", "uri": "/api/casinos/games?category=popular", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804123.185534, "end": 1732804139.788809, "duration": 16.603275060653687, "duration_str": "16.6s", "measures": [{"label": "Booting", "start": 1732804123.185534, "relative_start": 0, "end": **********.27385, "relative_end": **********.27385, "duration": 4.088315963745117, "duration_str": "4.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.273864, "relative_start": 4.088330030441284, "end": 1732804139.788813, "relative_end": 4.0531158447265625e-06, "duration": 12.514949083328247, "duration_str": "12.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14828008, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/casinos/games", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@allGames", "namespace": null, "prefix": "api/casinos", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=388\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:388-414</a>"}, "queries": {"nb_statements": 100, "nb_visible_statements": 100, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.8124799999999998, "accumulated_duration_str": "812ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'popular') and `status` = 1", "type": "query", "params": [], "bindings": ["popular", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.439198, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 0.218}, {"sql": "select * from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'popular') and `status` = 1 order by `views` desc limit 30 offset 0", "type": "query", "params": [], "bindings": ["popular", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.566717, "duration": 0.00883, "duration_str": "8.83ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 0.218, "width_percent": 1.087}, {"sql": "select * from `providers` where `providers`.`id` in (1, 15, 18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.710809, "duration": 0.00507, "duration_str": "5.07ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 1.305, "width_percent": 0.624}, {"sql": "select `categories`.*, `category_game`.`game_id` as `pivot_game_id`, `category_game`.`category_id` as `pivot_category_id` from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `category_game`.`game_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 972, 1101, 1103, 1126, 1141, 1149, 1150, 1152, 1155, 1160, 1163, 1164, 1167)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.796117, "duration": 0.03214, "duration_str": "32.14ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 1.929, "width_percent": 3.956}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1 limit 1", "type": "query", "params": [], "bindings": [********, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804135.627973, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 5.884, "width_percent": 0.214}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804135.705212, "duration": 0.00456, "duration_str": "4.56ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 6.099, "width_percent": 0.561}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1 limit 1", "type": "query", "params": [], "bindings": [********, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804135.760305, "duration": 0.0132, "duration_str": "13.2ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 6.66, "width_percent": 1.625}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804135.842362, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 8.285, "width_percent": 0.417}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 2 limit 1", "type": "query", "params": [], "bindings": [********, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804135.9030688, "duration": 0.01447, "duration_str": "14.47ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 8.702, "width_percent": 1.781}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 2 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804135.975504, "duration": 0.016489999999999998, "duration_str": "16.49ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 10.483, "width_percent": 2.03}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 2 limit 1", "type": "query", "params": [], "bindings": [********, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.0247521, "duration": 0.02877, "duration_str": "28.77ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 12.512, "width_percent": 3.541}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 2 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.1107209, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 16.053, "width_percent": 0.369}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 7 limit 1", "type": "query", "params": [], "bindings": [********, 7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.182122, "duration": 0.00674, "duration_str": "6.74ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 16.423, "width_percent": 0.83}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 7 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.233143, "duration": 0.019809999999999998, "duration_str": "19.81ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 17.252, "width_percent": 2.438}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 7 limit 1", "type": "query", "params": [], "bindings": [********, 7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.3071392, "duration": 0.01205, "duration_str": "12.05ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 19.69, "width_percent": 1.483}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 7 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.3651881, "duration": 0.02469, "duration_str": "24.69ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 21.173, "width_percent": 3.039}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 4 limit 1", "type": "query", "params": [], "bindings": [********, 4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.4399562, "duration": 0.01502, "duration_str": "15.02ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 24.212, "width_percent": 1.849}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 4 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.509856, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 26.061, "width_percent": 0.265}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 4 limit 1", "type": "query", "params": [], "bindings": [********, 4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.565517, "duration": 0.007980000000000001, "duration_str": "7.98ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 26.326, "width_percent": 0.982}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 4 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.615766, "duration": 0.016980000000000002, "duration_str": "16.98ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 27.308, "width_percent": 2.09}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 3 limit 1", "type": "query", "params": [], "bindings": [********, 3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.7050622, "duration": 0.01328, "duration_str": "13.28ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 29.398, "width_percent": 1.635}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 3 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.7948458, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 31.032, "width_percent": 0.053}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 3 limit 1", "type": "query", "params": [], "bindings": [********, 3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.8649662, "duration": 0.00453, "duration_str": "4.53ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 31.085, "width_percent": 0.558}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 3 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804136.932366, "duration": 0.023260000000000003, "duration_str": "23.26ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 31.643, "width_percent": 2.863}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 8 limit 1", "type": "query", "params": [], "bindings": [********, 8], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.007128, "duration": 0.01623, "duration_str": "16.23ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 34.505, "width_percent": 1.998}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 8 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.068008, "duration": 0.01893, "duration_str": "18.93ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 36.503, "width_percent": 2.33}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 8 limit 1", "type": "query", "params": [], "bindings": [********, 8], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.133172, "duration": 0.0125, "duration_str": "12.5ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 38.833, "width_percent": 1.538}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 8 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.180734, "duration": 0.032729999999999995, "duration_str": "32.73ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 40.371, "width_percent": 4.028}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 6 limit 1", "type": "query", "params": [], "bindings": [********, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.2622142, "duration": 0.00764, "duration_str": "7.64ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 44.4, "width_percent": 0.94}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 6 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.338512, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 45.34, "width_percent": 0.396}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 6 limit 1", "type": "query", "params": [], "bindings": [********, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.3833148, "duration": 0.01021, "duration_str": "10.21ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 45.737, "width_percent": 1.257}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 6 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.42907, "duration": 0.028820000000000002, "duration_str": "28.82ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 46.993, "width_percent": 3.547}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 11 limit 1", "type": "query", "params": [], "bindings": [********, 11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.50719, "duration": 0.022359999999999998, "duration_str": "22.36ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 50.54, "width_percent": 2.752}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 11 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.568585, "duration": 0.02844, "duration_str": "28.44ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 53.292, "width_percent": 3.5}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 11 limit 1", "type": "query", "params": [], "bindings": [********, 11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.642379, "duration": 0.00698, "duration_str": "6.98ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 56.793, "width_percent": 0.859}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 11 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.720449, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 57.652, "width_percent": 0.14}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 5 limit 1", "type": "query", "params": [], "bindings": [********, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.7843459, "duration": 0.01095, "duration_str": "10.95ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 57.792, "width_percent": 1.348}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 5 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.850977, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 59.14, "width_percent": 0.356}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 5 limit 1", "type": "query", "params": [], "bindings": [********, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.8955798, "duration": 0.0143, "duration_str": "14.3ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 59.496, "width_percent": 1.76}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 5 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804137.956745, "duration": 0.018850000000000002, "duration_str": "18.85ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 61.256, "width_percent": 2.32}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1126 limit 1", "type": "query", "params": [], "bindings": [********, 1126], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.029918, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 63.576, "width_percent": 0.668}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1126 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1126], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.073333, "duration": 0.02525, "duration_str": "25.25ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 64.244, "width_percent": 3.108}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1126 limit 1", "type": "query", "params": [], "bindings": [********, 1126], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.124835, "duration": 0.0255, "duration_str": "25.5ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 67.352, "width_percent": 3.139}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1126 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1126], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.218476, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 70.49, "width_percent": 0.394}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1152 limit 1", "type": "query", "params": [], "bindings": [********, 1152], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.289475, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 70.884, "width_percent": 0.209}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1152 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1152], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.329121, "duration": 0.02872, "duration_str": "28.72ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 71.093, "width_percent": 3.535}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1152 limit 1", "type": "query", "params": [], "bindings": [********, 1152], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.39974, "duration": 0.01051, "duration_str": "10.51ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 74.628, "width_percent": 1.294}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1152 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1152], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.474398, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 75.922, "width_percent": 0.476}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1101 limit 1", "type": "query", "params": [], "bindings": [********, 1101], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.532715, "duration": 0.01421, "duration_str": "14.21ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 76.398, "width_percent": 1.749}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1101 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1101], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.610106, "duration": 0.00544, "duration_str": "5.44ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 78.147, "width_percent": 0.67}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1101 limit 1", "type": "query", "params": [], "bindings": [********, 1101], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.66646, "duration": 0.01099, "duration_str": "10.99ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 78.817, "width_percent": 1.353}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1101 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1101], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.733643, "duration": 0.00749, "duration_str": "7.49ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 80.169, "width_percent": 0.922}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1150 limit 1", "type": "query", "params": [], "bindings": [********, 1150], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.770138, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 81.091, "width_percent": 0.052}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1150 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1150], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.7920258, "duration": 0.00498, "duration_str": "4.98ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 81.143, "width_percent": 0.613}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1150 limit 1", "type": "query", "params": [], "bindings": [********, 1150], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.81249, "duration": 0.0047, "duration_str": "4.7ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 81.756, "width_percent": 0.578}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1150 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1150], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.842683, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 82.334, "width_percent": 0.466}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1160 limit 1", "type": "query", "params": [], "bindings": [********, 1160], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.865407, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 82.801, "width_percent": 0.274}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1160 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1160], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.892909, "duration": 0.00888, "duration_str": "8.88ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 83.075, "width_percent": 1.093}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1160 limit 1", "type": "query", "params": [], "bindings": [********, 1160], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.917599, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 84.168, "width_percent": 0.082}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1160 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1160], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.9405832, "duration": 0.00518, "duration_str": "5.18ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 84.251, "width_percent": 0.638}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1167 limit 1", "type": "query", "params": [], "bindings": [********, 1167], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.971303, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 84.888, "width_percent": 0.309}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1167 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1167], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804138.992545, "duration": 0.0077800000000000005, "duration_str": "7.78ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 85.197, "width_percent": 0.958}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1167 limit 1", "type": "query", "params": [], "bindings": [********, 1167], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.021078, "duration": 0.00467, "duration_str": "4.67ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 86.155, "width_percent": 0.575}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1167 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1167], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.04652, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 86.73, "width_percent": 0.039}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 9 limit 1", "type": "query", "params": [], "bindings": [********, 9], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.065705, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 86.769, "width_percent": 0.305}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 9 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.088233, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 87.074, "width_percent": 0.042}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 9 limit 1", "type": "query", "params": [], "bindings": [********, 9], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.103731, "duration": 0.007549999999999999, "duration_str": "7.55ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 87.116, "width_percent": 0.929}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 9 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.134706, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 88.045, "width_percent": 0.353}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1141 limit 1", "type": "query", "params": [], "bindings": [********, 1141], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.1591878, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 88.398, "width_percent": 0.313}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1141 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1141], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.1791308, "duration": 0.00658, "duration_str": "6.58ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 88.711, "width_percent": 0.81}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1141 limit 1", "type": "query", "params": [], "bindings": [********, 1141], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.203955, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 89.521, "width_percent": 0.042}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1141 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1141], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.217266, "duration": 0.00861, "duration_str": "8.61ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 89.563, "width_percent": 1.06}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1164 limit 1", "type": "query", "params": [], "bindings": [********, 1164], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.246535, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 90.623, "width_percent": 0.556}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1164 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1164], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.270805, "duration": 0.00283, "duration_str": "2.83ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 91.179, "width_percent": 0.348}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1164 limit 1", "type": "query", "params": [], "bindings": [********, 1164], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.292152, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 91.527, "width_percent": 0.441}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1164 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1164], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.316213, "duration": 0.007809999999999999, "duration_str": "7.81ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 91.968, "width_percent": 0.961}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 972 limit 1", "type": "query", "params": [], "bindings": [********, 972], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.344665, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 92.929, "width_percent": 0.055}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 972 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [972], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.369085, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 92.984, "width_percent": 0.582}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 972 limit 1", "type": "query", "params": [], "bindings": [********, 972], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.390164, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 93.567, "width_percent": 0.439}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 972 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [972], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.4181712, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 94.006, "width_percent": 0.057}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1103 limit 1", "type": "query", "params": [], "bindings": [********, 1103], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.438174, "duration": 0.00692, "duration_str": "6.92ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 94.063, "width_percent": 0.852}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1103 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1103], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.4652429, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 94.914, "width_percent": 0.075}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1103 limit 1", "type": "query", "params": [], "bindings": [********, 1103], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.485019, "duration": 0.00902, "duration_str": "9.02ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 94.989, "width_percent": 1.11}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1103 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1103], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.5217671, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 96.1, "width_percent": 0.054}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1155 limit 1", "type": "query", "params": [], "bindings": [********, 1155], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.538172, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 96.154, "width_percent": 0.174}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1155 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1155], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.564776, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 96.327, "width_percent": 0.042}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1155 limit 1", "type": "query", "params": [], "bindings": [********, 1155], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.58255, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 96.369, "width_percent": 0.404}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1155 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1155], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.603651, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 96.773, "width_percent": 0.322}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1149 limit 1", "type": "query", "params": [], "bindings": [********, 1149], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.624555, "duration": 0.0057599999999999995, "duration_str": "5.76ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 97.095, "width_percent": 0.709}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1149 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1149], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.648799, "duration": 0.00875, "duration_str": "8.75ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 97.804, "width_percent": 1.077}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1149 limit 1", "type": "query", "params": [], "bindings": [********, 1149], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.6734898, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 98.881, "width_percent": 0.053}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1149 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1149], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.681396, "duration": 0.00561, "duration_str": "5.61ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 98.934, "width_percent": 0.69}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1163 limit 1", "type": "query", "params": [], "bindings": [********, 1163], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.70222, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 99.625, "width_percent": 0.059}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1163 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1163], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.718504, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 99.684, "width_percent": 0.05}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1163 limit 1", "type": "query", "params": [], "bindings": [********, 1163], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.731441, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 99.734, "width_percent": 0.065}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1163 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1163], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.7455509, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 99.799, "width_percent": 0.066}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 10 limit 1", "type": "query", "params": [], "bindings": [********, 10], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.752944, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 99.866, "width_percent": 0.039}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 10 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.760122, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 99.905, "width_percent": 0.034}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 10 limit 1", "type": "query", "params": [], "bindings": [********, 10], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.766227, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 99.94, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 10 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732804139.7737548, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 99.969, "width_percent": 0.031}]}, "models": {"data": {"App\\Models\\Category": {"value": 95, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Game": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=1", "ajax": false, "filename": "Game.php", "line": "?"}}, "App\\Models\\Provider": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FProvider.php&line=1", "ajax": false, "filename": "Provider.php", "line": "?"}}}, "count": 122, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/casinos/games", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-685625434 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">popular</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-685625434\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-497281150 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"758 characters\">_fbp=fb.1.1732723263756.71770186546337922; XSRF-TOKEN=eyJpdiI6IkZqOGF5aC81cmpWZVNRcm9QZGwrdVE9PSIsInZhbHVlIjoieG5NMVBnQm5TeXZnRXpLbzJjTXo0OHlFRHBVOFFHVjYvUndsRWdBZGIxNkFqNjVab2I2NVZHZEN6YVpxSHY4RWR2MTRTQUhRU0JBYjNOa3NSUFp5MUFrbmI4d0R4ekptdmJ1bVI3VnlVSEx5MGVSNDlDT1A4dk1IWDhyQ1hYbksiLCJtYWMiOiJiYTI4N2EwNTc4OWFhOWVlZWFkZmRjNTQ5MWQ4N2M3ZDg3NjFkNDRhY2ExOGI0MTFiMmQyNTgxMGQzYjkzYzA4IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6ImdzaExtZFhmNmJrS0xFM0RLZjdiOUE9PSIsInZhbHVlIjoiSnJraXhUbEo3cGsrODhaSEttRGFZakt3Q0k1bFZRNFFmQmdhYmRNY2plSHovR21KVElESGRPa2dMb280SCtIKzZLVWVwaFVEOExYa1J1ZVVOaFFwUnVCSTdGN2NRVHFKem1sakNDTUhaSEdEaVZwOFM0OElKRXFaTm03RGdnWU4iLCJtYWMiOiJiOWE3Y2M2MzY5NTBmYmQ1YTc2YTNjOTkwY2RiMDk5ZjU5ZGEyMTRlM2U4MGZjNjA3M2M5ZjRlZTVmZGYzODBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"133 characters\">Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZqOGF5aC81cmpWZVNRcm9QZGwrdVE9PSIsInZhbHVlIjoieG5NMVBnQm5TeXZnRXpLbzJjTXo0OHlFRHBVOFFHVjYvUndsRWdBZGIxNkFqNjVab2I2NVZHZEN6YVpxSHY4RWR2MTRTQUhRU0JBYjNOa3NSUFp5MUFrbmI4d0R4ekptdmJ1bVI3VnlVSEx5MGVSNDlDT1A4dk1IWDhyQ1hYbksiLCJtYWMiOiJiYTI4N2EwNTc4OWFhOWVlZWFkZmRjNTQ5MWQ4N2M3ZDg3NjFkNDRhY2ExOGI0MTFiMmQyNTgxMGQzYjkzYzA4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ryhn09EKvtnlPa25E5IgpPwzgD9rUjhFMPeBzANn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497281150\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-617171066 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_fbp</span>\" => \"<span class=sf-dump-str title=\"36 characters\">fb.1.1732723263756.71770186546337922</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZqOGF5aC81cmpWZVNRcm9QZGwrdVE9PSIsInZhbHVlIjoieG5NMVBnQm5TeXZnRXpLbzJjTXo0OHlFRHBVOFFHVjYvUndsRWdBZGIxNkFqNjVab2I2NVZHZEN6YVpxSHY4RWR2MTRTQUhRU0JBYjNOa3NSUFp5MUFrbmI4d0R4ekptdmJ1bVI3VnlVSEx5MGVSNDlDT1A4dk1IWDhyQ1hYbksiLCJtYWMiOiJiYTI4N2EwNTc4OWFhOWVlZWFkZmRjNTQ5MWQ4N2M3ZDg3NjFkNDRhY2ExOGI0MTFiMmQyNTgxMGQzYjkzYzA4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImdzaExtZFhmNmJrS0xFM0RLZjdiOUE9PSIsInZhbHVlIjoiSnJraXhUbEo3cGsrODhaSEttRGFZakt3Q0k1bFZRNFFmQmdhYmRNY2plSHovR21KVElESGRPa2dMb280SCtIKzZLVWVwaFVEOExYa1J1ZVVOaFFwUnVCSTdGN2NRVHFKem1sakNDTUhaSEdEaVZwOFM0OElKRXFaTm03RGdnWU4iLCJtYWMiOiJiOWE3Y2M2MzY5NTBmYmQ1YTc2YTNjOTkwY2RiMDk5ZjU5ZGEyMTRlM2U4MGZjNjA3M2M5ZjRlZTVmZGYzODBhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-617171066\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1751677876 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:28:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751677876\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1298528276 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1298528276\", {\"maxDepth\":0})</script>\n"}}