{"__meta": {"id": "Xe442fb662b14cc264da568a0952f77e8", "datetime": "2024-11-28 13:07:56", "utime": 1732810076.659362, "method": "GET", "uri": "/api/profile/wallet/update-bonus-if-needed", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732810067.738349, "end": 1732810076.65938, "duration": 8.92103099822998, "duration_str": "8.92s", "measures": [{"label": "Booting", "start": 1732810067.738349, "relative_start": 0, "end": **********.124979, "relative_end": **********.124979, "duration": 2.386630058288574, "duration_str": "2.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.124999, "relative_start": 2.3866500854492188, "end": 1732810076.659382, "relative_end": 2.1457672119140625e-06, "duration": 6.534383058547974, "duration_str": "6.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14293344, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/profile/wallet/update-bonus-if-needed", "middleware": "api, auth:api", "controller": "App\\Http\\Controllers\\Api\\Profile\\WalletController@updateBonusIfNeeded", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=190\" onclick=\"\">app/Http/Controllers/Api/Profile/WalletController.php:190-209</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03688, "accumulated_duration_str": "36.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `wallets` where `user_id` = ******** limit 1", "type": "query", "params": [], "bindings": [********], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 193}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.240912, "duration": 0.03003, "duration_str": "30.03ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:193", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=193", "ajax": false, "filename": "WalletController.php", "line": "193"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 81.426}, {"sql": "update `wallets` set `balance_bonus_rollover` = 0, `wallets`.`updated_at` = '2024-11-28 13:07:50' where `id` = 279", "type": "query", "params": [], "bindings": [0, "2024-11-28 13:07:50", 279], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 204}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.342734, "duration": 0.006849999999999999, "duration_str": "6.85ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:204", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=204", "ajax": false, "filename": "WalletController.php", "line": "204"}, "connection": "china15tema2", "explain": null, "start_percent": 81.426, "width_percent": 18.574}]}, "models": {"data": {"App\\Models\\Wallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/profile/wallet/update-bonus-if-needed", "status_code": "<pre class=sf-dump id=sf-dump-1360182639 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1360182639\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-794257830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-794257830\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1298899763 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1298899763\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1460941841 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6Inp4SHNSSzBzaHVSZGlqdjZpbzdPTEE9PSIsInZhbHVlIjoiZTlaWjJYVzM4ZFFHdkU1T0pWWnVQVEpyZlJvU3pNL3pGSDFxV2MzekFjM1RON3dhbU1yTTV2WEc4NzZNWnd2MEdtQTFIUEg3NmJycS84Ui94RVR2QzMwbXJVZWJyL3d6V2l5YjJHZkhOS1ZicUl4UHNxMXVQdzBwSDVhMkZQSXIiLCJtYWMiOiIyNTM4NWQ1NzUxNGZjNTI5ZDlmMjk1Njc4YmI2ODlkMzg5YzA1Njc3MTJhM2EwNzRiZjNjOTJkYTM1OTk5OTQ3IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6Ik1GcGx1L1VWa3NrTTZYaVF3K0cxUUE9PSIsInZhbHVlIjoiRk1wZDZ0TjJFTkloN1MxOEJqKzBjZFFjVUxtMTRUS0xzeXpibGxYb0Q1c2ltYUhoYjNjekdwelJuUFcrS2MxVjZsVGlROWRUMzNvL09MN1U3RWVDaGo2M2pUQ2cwTkp2UmhTWU0vUHptTnRNallpRVpqS1pWTTk1TkxKOFhHTzIiLCJtYWMiOiI1MWRkOTNmYjgzYjdiZWJhNWU4NjdiMTQxNGM5YTJkNDBlOTUwYjQzMjJiZThkODQwNGNkZWNlM2E2ZTNmZjMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"133 characters\">Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Inp4SHNSSzBzaHVSZGlqdjZpbzdPTEE9PSIsInZhbHVlIjoiZTlaWjJYVzM4ZFFHdkU1T0pWWnVQVEpyZlJvU3pNL3pGSDFxV2MzekFjM1RON3dhbU1yTTV2WEc4NzZNWnd2MEdtQTFIUEg3NmJycS84Ui94RVR2QzMwbXJVZWJyL3d6V2l5YjJHZkhOS1ZicUl4UHNxMXVQdzBwSDVhMkZQSXIiLCJtYWMiOiIyNTM4NWQ1NzUxNGZjNTI5ZDlmMjk1Njc4YmI2ODlkMzg5YzA1Njc3MTJhM2EwNzRiZjNjOTJkYTM1OTk5OTQ3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QZTB9Q66Z6TZdPmWFr3VIl4w177WleBhzBYndcG6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460941841\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1187160558 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Inp4SHNSSzBzaHVSZGlqdjZpbzdPTEE9PSIsInZhbHVlIjoiZTlaWjJYVzM4ZFFHdkU1T0pWWnVQVEpyZlJvU3pNL3pGSDFxV2MzekFjM1RON3dhbU1yTTV2WEc4NzZNWnd2MEdtQTFIUEg3NmJycS84Ui94RVR2QzMwbXJVZWJyL3d6V2l5YjJHZkhOS1ZicUl4UHNxMXVQdzBwSDVhMkZQSXIiLCJtYWMiOiIyNTM4NWQ1NzUxNGZjNTI5ZDlmMjk1Njc4YmI2ODlkMzg5YzA1Njc3MTJhM2EwNzRiZjNjOTJkYTM1OTk5OTQ3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik1GcGx1L1VWa3NrTTZYaVF3K0cxUUE9PSIsInZhbHVlIjoiRk1wZDZ0TjJFTkloN1MxOEJqKzBjZFFjVUxtMTRUS0xzeXpibGxYb0Q1c2ltYUhoYjNjekdwelJuUFcrS2MxVjZsVGlROWRUMzNvL09MN1U3RWVDaGo2M2pUQ2cwTkp2UmhTWU0vUHptTnRNallpRVpqS1pWTTk1TkxKOFhHTzIiLCJtYWMiOiI1MWRkOTNmYjgzYjdiZWJhNWU4NjdiMTQxNGM5YTJkNDBlOTUwYjQzMjJiZThkODQwNGNkZWNlM2E2ZTNmZjMxIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1187160558\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2089281375 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 16:07:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089281375\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1746452360 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1746452360\", {\"maxDepth\":0})</script>\n"}}