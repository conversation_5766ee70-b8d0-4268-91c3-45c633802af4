{"__meta": {"id": "Xd0934b920eb5e7b5d8d050c042f155c4", "datetime": "2024-11-28 11:30:23", "utime": 1732804223.007912, "method": "GET", "uri": "/api/casinos/games?category=ao-vivo", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804210.781644, "end": 1732804223.007929, "duration": 12.226284980773926, "duration_str": "12.23s", "measures": [{"label": "Booting", "start": 1732804210.781644, "relative_start": 0, "end": **********.098644, "relative_end": **********.098644, "duration": 4.316999912261963, "duration_str": "4.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.098672, "relative_start": 4.317027807235718, "end": 1732804223.007931, "relative_end": 1.9073486328125e-06, "duration": 7.909259080886841, "duration_str": "7.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14332824, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/casinos/games", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@allGames", "namespace": null, "prefix": "api/casinos", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=388\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:388-414</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0050999999999999995, "accumulated_duration_str": "5.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'ao-vivo') and `status` = 1", "type": "query", "params": [], "bindings": ["ao-vivo", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.267899, "duration": 0.0050999999999999995, "duration_str": "5.1ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/casinos/games", "status_code": "<pre class=sf-dump id=sf-dump-483829047 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-483829047\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-706720865 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ao-vivo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706720865\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-645877688 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-645877688\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1974792594 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"758 characters\">_fbp=fb.1.1732723263756.71770186546337922; XSRF-TOKEN=eyJpdiI6IjdDclNIQkNmVnN3YnFqMFMyQzBtQmc9PSIsInZhbHVlIjoiSmNEMzg4MkFuKzQzRlV3TmR1eU1iWDl6cUhYTTVuREw5ekRNcitOVWtZd3hIK1B1dktuT3BROE1nTjkxZERhTnB2N2VhUS8wK0hvaWEycDV0ejRqaXAvR052RXl1d0ZDTGlDMFJRVEl3T1BSTWtGOE51MThDN2RTNGVib1F5NWciLCJtYWMiOiJjZDFmMTA2NzY5OWM3ZjJlNGNhOGMyOGZlMWU3MGVhZTI2ZGZlYjBjNTVlZTA2MGQzMjIyMzIxNzBhYzUyZWIyIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6InQxYitmSzVHS0tXby9zSXlXNjlsQ1E9PSIsInZhbHVlIjoiRkF4aGd6N08zaFU4UUhPY1dLakphOUhZaGJ4YkI5STVNV3NhNUMxYnBZQjVnNUZwRXhtV2lnMDlLUi9oZ0Y5UUFzWFFhdXA0MXExRnRlY0c3WVRheHhQeFIyeEsvaTdXa0d6am1hdzV5TnlYQ2hrK0RhdVQrSnlIOFZScXFLWk8iLCJtYWMiOiJmMzI2MzJlNDA5NWY3M2RmZTBhZjVkMzQ1YTEwZmQyOWNlNjE0ZTFiMzI0ZThmZGVjNzBjMTlhMDU5NDllM2FiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjdDclNIQkNmVnN3YnFqMFMyQzBtQmc9PSIsInZhbHVlIjoiSmNEMzg4MkFuKzQzRlV3TmR1eU1iWDl6cUhYTTVuREw5ekRNcitOVWtZd3hIK1B1dktuT3BROE1nTjkxZERhTnB2N2VhUS8wK0hvaWEycDV0ejRqaXAvR052RXl1d0ZDTGlDMFJRVEl3T1BSTWtGOE51MThDN2RTNGVib1F5NWciLCJtYWMiOiJjZDFmMTA2NzY5OWM3ZjJlNGNhOGMyOGZlMWU3MGVhZTI2ZGZlYjBjNTVlZTA2MGQzMjIyMzIxNzBhYzUyZWIyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ryhn09EKvtnlPa25E5IgpPwzgD9rUjhFMPeBzANn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974792594\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1695030411 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_fbp</span>\" => \"<span class=sf-dump-str title=\"36 characters\">fb.1.1732723263756.71770186546337922</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjdDclNIQkNmVnN3YnFqMFMyQzBtQmc9PSIsInZhbHVlIjoiSmNEMzg4MkFuKzQzRlV3TmR1eU1iWDl6cUhYTTVuREw5ekRNcitOVWtZd3hIK1B1dktuT3BROE1nTjkxZERhTnB2N2VhUS8wK0hvaWEycDV0ejRqaXAvR052RXl1d0ZDTGlDMFJRVEl3T1BSTWtGOE51MThDN2RTNGVib1F5NWciLCJtYWMiOiJjZDFmMTA2NzY5OWM3ZjJlNGNhOGMyOGZlMWU3MGVhZTI2ZGZlYjBjNTVlZTA2MGQzMjIyMzIxNzBhYzUyZWIyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InQxYitmSzVHS0tXby9zSXlXNjlsQ1E9PSIsInZhbHVlIjoiRkF4aGd6N08zaFU4UUhPY1dLakphOUhZaGJ4YkI5STVNV3NhNUMxYnBZQjVnNUZwRXhtV2lnMDlLUi9oZ0Y5UUFzWFFhdXA0MXExRnRlY0c3WVRheHhQeFIyeEsvaTdXa0d6am1hdzV5TnlYQ2hrK0RhdVQrSnlIOFZScXFLWk8iLCJtYWMiOiJmMzI2MzJlNDA5NWY3M2RmZTBhZjVkMzQ1YTEwZmQyOWNlNjE0ZTFiMzI0ZThmZGVjNzBjMTlhMDU5NDllM2FiIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695030411\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1743582830 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:30:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1743582830\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-31955362 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-31955362\", {\"maxDepth\":0})</script>\n"}}