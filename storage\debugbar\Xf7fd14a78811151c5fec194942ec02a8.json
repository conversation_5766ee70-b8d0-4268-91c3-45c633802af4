{"__meta": {"id": "Xf7fd14a78811151c5fec194942ec02a8", "datetime": "2024-11-28 17:07:31", "utime": 1732824451.847579, "method": "GET", "uri": "/api/slider-text", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732824443.101612, "end": 1732824451.847598, "duration": 8.745985984802246, "duration_str": "8.75s", "measures": [{"label": "Booting", "start": 1732824443.101612, "relative_start": 0, "end": **********.803401, "relative_end": **********.803401, "duration": 2.701788902282715, "duration_str": "2.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.803413, "relative_start": 2.70180082321167, "end": 1732824451.847602, "relative_end": 3.814697265625e-06, "duration": 6.044188976287842, "duration_str": "6.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14321712, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/slider-text", "middleware": "api", "controller": "App\\Http\\Controllers\\SliderTextController@index", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FSliderTextController.php&line=10\" onclick=\"\">app/Http/Controllers/SliderTextController.php:10-18</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02186, "accumulated_duration_str": "21.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `id`, `message` from `slider_texts`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SliderTextController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/SliderTextController.php", "line": 13}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.904495, "duration": 0.02186, "duration_str": "21.86ms", "memory": 0, "memory_str": null, "filename": "SliderTextController.php:13", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/SliderTextController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/SliderTextController.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FSliderTextController.php&line=13", "ajax": false, "filename": "SliderTextController.php", "line": "13"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\SliderText": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FSliderText.php&line=1", "ajax": false, "filename": "SliderText.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/slider-text", "status_code": "<pre class=sf-dump id=sf-dump-1363742711 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1363742711\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1473671679 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1473671679\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-575420421 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-575420421\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-59585034 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IkZvd093Zzk1NmpXbndlMzQvYW5Zenc9PSIsInZhbHVlIjoibTc1WmxtZ2ttM1hSZFkxMXRUV2NoMU5tb1k5a2o2RWhQRmZCekZ2RW5hc3AxQWJiSVB6Qm9hdzR3Z3VDa1NNR3pxeStGUm5GQU9rZ2k3dXQ3TVNSb25TUFUvQXcxcFhoNCtQMWJKc1ZabDFZbXAvSWgxZG9MM3ZiTlhMeVFFUloiLCJtYWMiOiI2MzExNjI2NzUyNTljMzVmZmI0MGUzY2FhNmM0NzIzZGRhYTVhZjNmYjU4ODJlNDFlZDdhNzdkMzU0MmFjOWM3IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6IlQvVHd1NG00aTdnL2N4LzhaTHFwL0E9PSIsInZhbHVlIjoiYWhXNXBQOWRSNFZDbmUxT282dVhodHlDSlFBd29hY05Lb3ZFdVFQa3V6cFRJTEFmNk1nQk9PU1VHYWVVOGtyZkh6Z2RIRk9hM2wxRnlGalpaVEEwdkNiSHhDdUdLaGUwSXlkWUFrbm16MnV4dEdpTXBxcXA1Y3hNTXVGQ21aN2ciLCJtYWMiOiJmOWZjODUxZWMxYmJmMzdjM2JmOTM0NTVhYWIwZWU1MDU4YWY4NjA4ZWE0OTlmNDYzZWNhZDRjYmYwMGFiNDcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZvd093Zzk1NmpXbndlMzQvYW5Zenc9PSIsInZhbHVlIjoibTc1WmxtZ2ttM1hSZFkxMXRUV2NoMU5tb1k5a2o2RWhQRmZCekZ2RW5hc3AxQWJiSVB6Qm9hdzR3Z3VDa1NNR3pxeStGUm5GQU9rZ2k3dXQ3TVNSb25TUFUvQXcxcFhoNCtQMWJKc1ZabDFZbXAvSWgxZG9MM3ZiTlhMeVFFUloiLCJtYWMiOiI2MzExNjI2NzUyNTljMzVmZmI0MGUzY2FhNmM0NzIzZGRhYTVhZjNmYjU4ODJlNDFlZDdhNzdkMzU0MmFjOWM3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QZTB9Q66Z6TZdPmWFr3VIl4w177WleBhzBYndcG6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59585034\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZvd093Zzk1NmpXbndlMzQvYW5Zenc9PSIsInZhbHVlIjoibTc1WmxtZ2ttM1hSZFkxMXRUV2NoMU5tb1k5a2o2RWhQRmZCekZ2RW5hc3AxQWJiSVB6Qm9hdzR3Z3VDa1NNR3pxeStGUm5GQU9rZ2k3dXQ3TVNSb25TUFUvQXcxcFhoNCtQMWJKc1ZabDFZbXAvSWgxZG9MM3ZiTlhMeVFFUloiLCJtYWMiOiI2MzExNjI2NzUyNTljMzVmZmI0MGUzY2FhNmM0NzIzZGRhYTVhZjNmYjU4ODJlNDFlZDdhNzdkMzU0MmFjOWM3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlQvVHd1NG00aTdnL2N4LzhaTHFwL0E9PSIsInZhbHVlIjoiYWhXNXBQOWRSNFZDbmUxT282dVhodHlDSlFBd29hY05Lb3ZFdVFQa3V6cFRJTEFmNk1nQk9PU1VHYWVVOGtyZkh6Z2RIRk9hM2wxRnlGalpaVEEwdkNiSHhDdUdLaGUwSXlkWUFrbm16MnV4dEdpTXBxcXA1Y3hNTXVGQ21aN2ciLCJtYWMiOiJmOWZjODUxZWMxYmJmMzdjM2JmOTM0NTVhYWIwZWU1MDU4YWY4NjA4ZWE0OTlmNDYzZWNhZDRjYmYwMGFiNDcwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1787369829 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 20:07:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787369829\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1692462953 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1692462953\", {\"maxDepth\":0})</script>\n"}}