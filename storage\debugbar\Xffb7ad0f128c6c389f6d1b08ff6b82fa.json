{"__meta": {"id": "Xffb7ad0f128c6c389f6d1b08ff6b82fa", "datetime": "2024-11-28 17:14:42", "utime": 1732824882.20794, "method": "GET", "uri": "/api/slider-text", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732824873.571121, "end": 1732824882.208639, "duration": 8.637517929077148, "duration_str": "8.64s", "measures": [{"label": "Booting", "start": 1732824873.571121, "relative_start": 0, "end": **********.697301, "relative_end": **********.697301, "duration": 3.1261799335479736, "duration_str": "3.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.698875, "relative_start": 3.127753973007202, "end": 1732824882.208643, "relative_end": 4.0531158447265625e-06, "duration": 5.509768009185791, "duration_str": "5.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14306104, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/slider-text", "middleware": "api", "controller": "App\\Http\\Controllers\\SliderTextController@index", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FSliderTextController.php&line=10\" onclick=\"\">app/Http/Controllers/SliderTextController.php:10-18</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "65.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `id`, `message` from `slider_texts`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SliderTextController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/SliderTextController.php", "line": 13}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.822157, "duration": 0.*****************, "duration_str": "65.37ms", "memory": 0, "memory_str": null, "filename": "SliderTextController.php:13", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/SliderTextController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/SliderTextController.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FSliderTextController.php&line=13", "ajax": false, "filename": "SliderTextController.php", "line": "13"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\SliderText": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FSliderText.php&line=1", "ajax": false, "filename": "SliderText.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/slider-text", "status_code": "<pre class=sf-dump id=sf-dump-659894811 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-659894811\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-524619592 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-524619592\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-760848127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-760848127\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-464551236 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6InFJeVQ1bDVvdCsyZGt6L0NqTzMyN2c9PSIsInZhbHVlIjoiM2Myd2RjQzU4a0czd1FkdnJGMlZ2RFNBRkttcHptMlR6dDRJeGtCOFJVY25odGt6VnNuVkRXS29yc0dMN1E5QkRHTE02U3RCc2pITHJKUUFoZWdPRFFtVlFZenNId1hNWEFFaTN4eXpSbnRlZW1Xd0dtL0FOK1RXcVkrSmQzeDAiLCJtYWMiOiJmN2QzNTgyMzAyMWEzYTRlMGU4NGYxZWVhMDQ4ZDRjNjI5MGE0MzZiODExMjgyMWJjNGNkMzM1ZDM3MWFiYzI0IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6ImpBZ3NRa3VUVFhiOWlQZXR5QklXK3c9PSIsInZhbHVlIjoiR3FsYVVWOS9HclJTVlU1ZFFtbVNDcmhxMnJ5RHBzaVpZaWJsOXFGUk9aWUFjdUxmM2JDMUtjQVlraUVJUThLSTQ0R2xnYnZBd0l1SHRCZHlza1VwWVBHSE1mbk5jTVVCWFhTL3ZiTlRIRlNieWY1WS92ekdZSVhBOGk3eFc1V1IiLCJtYWMiOiJlYjJjMTMxZjg2MDlmYzA3NDg0NTU0YjFhZThkMmUwMGI3NjJlNDkwYTNhMzdlMTM0ZGQ4NWU1YjI4OGY5NTVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InFJeVQ1bDVvdCsyZGt6L0NqTzMyN2c9PSIsInZhbHVlIjoiM2Myd2RjQzU4a0czd1FkdnJGMlZ2RFNBRkttcHptMlR6dDRJeGtCOFJVY25odGt6VnNuVkRXS29yc0dMN1E5QkRHTE02U3RCc2pITHJKUUFoZWdPRFFtVlFZenNId1hNWEFFaTN4eXpSbnRlZW1Xd0dtL0FOK1RXcVkrSmQzeDAiLCJtYWMiOiJmN2QzNTgyMzAyMWEzYTRlMGU4NGYxZWVhMDQ4ZDRjNjI5MGE0MzZiODExMjgyMWJjNGNkMzM1ZDM3MWFiYzI0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">SEQh5A9KfymKvd9MOfVVxKhc4l18gY5GcDvSYy8E</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-464551236\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-659778253 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InFJeVQ1bDVvdCsyZGt6L0NqTzMyN2c9PSIsInZhbHVlIjoiM2Myd2RjQzU4a0czd1FkdnJGMlZ2RFNBRkttcHptMlR6dDRJeGtCOFJVY25odGt6VnNuVkRXS29yc0dMN1E5QkRHTE02U3RCc2pITHJKUUFoZWdPRFFtVlFZenNId1hNWEFFaTN4eXpSbnRlZW1Xd0dtL0FOK1RXcVkrSmQzeDAiLCJtYWMiOiJmN2QzNTgyMzAyMWEzYTRlMGU4NGYxZWVhMDQ4ZDRjNjI5MGE0MzZiODExMjgyMWJjNGNkMzM1ZDM3MWFiYzI0IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImpBZ3NRa3VUVFhiOWlQZXR5QklXK3c9PSIsInZhbHVlIjoiR3FsYVVWOS9HclJTVlU1ZFFtbVNDcmhxMnJ5RHBzaVpZaWJsOXFGUk9aWUFjdUxmM2JDMUtjQVlraUVJUThLSTQ0R2xnYnZBd0l1SHRCZHlza1VwWVBHSE1mbk5jTVVCWFhTL3ZiTlRIRlNieWY1WS92ekdZSVhBOGk3eFc1V1IiLCJtYWMiOiJlYjJjMTMxZjg2MDlmYzA3NDg0NTU0YjFhZThkMmUwMGI3NjJlNDkwYTNhMzdlMTM0ZGQ4NWU1YjI4OGY5NTVkIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659778253\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-691948420 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 20:14:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691948420\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1782946372 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1782946372\", {\"maxDepth\":0})</script>\n"}}