{"__meta": {"id": "Xcac96796c0b3c3b991a950fe5327b08b", "datetime": "2024-11-28 14:00:18", "utime": 1732813218.223198, "method": "GET", "uri": "/ad-min-can-admin/banners/29/edit", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732813213.904265, "end": 1732813218.223219, "duration": 4.318953990936279, "duration_str": "4.32s", "measures": [{"label": "Booting", "start": 1732813213.904265, "relative_start": 0, "end": 1732813215.002576, "relative_end": 1732813215.002576, "duration": 1.098311185836792, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732813215.002596, "relative_start": 1.0983309745788574, "end": 1732813218.223221, "relative_end": 2.1457672119140625e-06, "duration": 3.220625162124634, "duration_str": "3.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14090392, "peak_usage_str": "13MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.205872, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::eeb545c356a817f56ecf5e68001e6e4d", "param_count": null, "params": [], "start": **********.222546, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/eeb545c356a817f56ecf5e68001e6e4d.blade.php__components::eeb545c356a817f56ecf5e68001e6e4d", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Feeb545c356a817f56ecf5e68001e6e4d.blade.php&line=1", "ajax": false, "filename": "eeb545c356a817f56ecf5e68001e6e4d.blade.php", "line": "?"}}, {"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.236244, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::bb2a00dce2af7923758c4a15eaed50fb", "param_count": null, "params": [], "start": **********.255827, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/bb2a00dce2af7923758c4a15eaed50fb.blade.php__components::bb2a00dce2af7923758c4a15eaed50fb", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Fbb2a00dce2af7923758c4a15eaed50fb.blade.php&line=1", "ajax": false, "filename": "bb2a00dce2af7923758c4a15eaed50fb.blade.php", "line": "?"}}, {"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.266516, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::24df2039ca2553dede44c819ca2b3052", "param_count": null, "params": [], "start": **********.2817, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/24df2039ca2553dede44c819ca2b3052.blade.php__components::24df2039ca2553dede44c819ca2b3052", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F24df2039ca2553dede44c819ca2b3052.blade.php&line=1", "ajax": false, "filename": "24df2039ca2553dede44c819ca2b3052.blade.php", "line": "?"}}, {"name": "__components::24df2039ca2553dede44c819ca2b3052", "param_count": null, "params": [], "start": **********.287967, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/24df2039ca2553dede44c819ca2b3052.blade.php__components::24df2039ca2553dede44c819ca2b3052", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F24df2039ca2553dede44c819ca2b3052.blade.php&line=1", "ajax": false, "filename": "24df2039ca2553dede44c819ca2b3052.blade.php", "line": "?"}}, {"name": "__components::24df2039ca2553dede44c819ca2b3052", "param_count": null, "params": [], "start": **********.302252, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/24df2039ca2553dede44c819ca2b3052.blade.php__components::24df2039ca2553dede44c819ca2b3052", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F24df2039ca2553dede44c819ca2b3052.blade.php&line=1", "ajax": false, "filename": "24df2039ca2553dede44c819ca2b3052.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.320658, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament.components.logo", "param_count": null, "params": [], "start": **********.775804, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/resources/views/filament/components/logo.blade.phpfilament.components.logo", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fresources%2Fviews%2Ffilament%2Fcomponents%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}}]}, "route": {"uri": "GET ad-min-can-admin/banners/{record}/edit", "domain": null, "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\CheckAdmin", "excluded_middleware": [], "controller": "App\\Filament\\Admin\\Resources\\BannerResource\\Pages\\EditBanner@__invoke", "as": "filament.admin.resources.banners.edit", "namespace": null, "prefix": "ad-min-can-admin/banners", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.037590000000000005, "accumulated_duration_str": "37.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}], "start": **********.907333, "duration": 0.011970000000000001, "duration_str": "11.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 31.844}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/User.php", "line": 176}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 34}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 183}], "start": **********.951747, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "china15tema2", "explain": null, "start_percent": 31.844, "width_percent": 12.237}, {"sql": "select * from `banners` where `id` = '29' limit 1", "type": "query", "params": [], "bindings": ["29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 192}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Pages/EditRecord.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.995784, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Resource.php:192", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 192}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource.php&line=192", "ajax": false, "filename": "Resource.php", "line": "192"}, "connection": "china15tema2", "explain": null, "start_percent": 44.081, "width_percent": 1.516}, {"sql": "select count(*) as aggregate from `deposits` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.3257759, "duration": 0.00497, "duration_str": "4.97ms", "memory": 0, "memory_str": null, "filename": "DepositResource.php:53", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FDepositResource.php&line=53", "ajax": false, "filename": "DepositResource.php", "line": "53"}, "connection": "china15tema2", "explain": null, "start_percent": 45.597, "width_percent": 13.222}, {"sql": "select count(*) as aggregate from `deposits` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.350999, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DepositResource.php:61", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FDepositResource.php&line=61", "ajax": false, "filename": "DepositResource.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 58.819, "width_percent": 1.304}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.389177, "duration": 0.00926, "duration_str": "9.26ms", "memory": 0, "memory_str": null, "filename": "WithdrawalResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FWithdrawalResource.php&line=49", "ajax": false, "filename": "WithdrawalResource.php", "line": "49"}, "connection": "china15tema2", "explain": null, "start_percent": 60.122, "width_percent": 24.634}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.4328692, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "WithdrawalResource.php:54", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FWithdrawalResource.php&line=54", "ajax": false, "filename": "WithdrawalResource.php", "line": "54"}, "connection": "china15tema2", "explain": null, "start_percent": 84.757, "width_percent": 1.224}, {"sql": "select count(*) as aggregate from `deposits` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.6338491, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "DepositResource.php:53", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FDepositResource.php&line=53", "ajax": false, "filename": "DepositResource.php", "line": "53"}, "connection": "china15tema2", "explain": null, "start_percent": 85.98, "width_percent": 10.136}, {"sql": "select count(*) as aggregate from `deposits` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.6687598, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "DepositResource.php:61", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FDepositResource.php&line=61", "ajax": false, "filename": "DepositResource.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 96.116, "width_percent": 1.144}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.700933, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "WithdrawalResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FWithdrawalResource.php&line=49", "ajax": false, "filename": "WithdrawalResource.php", "line": "49"}, "connection": "china15tema2", "explain": null, "start_percent": 97.26, "width_percent": 1.144}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.7380328, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "WithdrawalResource.php:54", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FWithdrawalResource.php&line=54", "ajax": false, "filename": "WithdrawalResource.php", "line": "54"}, "connection": "china15tema2", "explain": null, "start_percent": 98.404, "width_percent": 1.596}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Banner": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FBanner.php&line=1", "ajax": false, "filename": "Banner.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": {"app.filament.admin.resources.banner-resource.pages.edit-banner #c3sCEVO1suTtJnF7G2Kt": "array:4 [\n  \"data\" => array:19 [\n    \"data\" => array:9 [\n      \"id\" => 29\n      \"link\" => \"/\"\n      \"image\" => array:1 [\n        \"1995bbd8-71c1-4fb4-a5df-374d3ad80d22\" => \"01JDSVEM0WT3TW12PR0QVBVN9P.webp\"\n      ]\n      \"type\" => \"carousel\"\n      \"description\" => null\n      \"created_at\" => \"2024-11-28T17:00:13.000000Z\"\n      \"updated_at\" => \"2024-11-28T17:00:13.000000Z\"\n      \"mobile_image\" => \"\"\n      \"approval_password_save\" => null\n    ]\n    \"previousUrl\" => \"https://job.forradapg.com/ad-min-can-admin/banners/create\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Banner {#2745\n      #connection: \"mysql\"\n      #table: \"banners\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:8 [\n        \"id\" => 29\n        \"link\" => \"/\"\n        \"image\" => \"01JDSVEM0WT3TW12PR0QVBVN9P.webp\"\n        \"type\" => \"carousel\"\n        \"description\" => null\n        \"created_at\" => \"2024-11-28 14:00:13\"\n        \"updated_at\" => \"2024-11-28 14:00:13\"\n        \"mobile_image\" => \"\"\n      ]\n      #original: array:8 [\n        \"id\" => 29\n        \"link\" => \"/\"\n        \"image\" => \"01JDSVEM0WT3TW12PR0QVBVN9P.webp\"\n        \"type\" => \"carousel\"\n        \"description\" => null\n        \"created_at\" => \"2024-11-28 14:00:13\"\n        \"updated_at\" => \"2024-11-28 14:00:13\"\n        \"mobile_image\" => \"\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:4 [\n        0 => \"image\"\n        1 => \"type\"\n        2 => \"description\"\n        3 => \"link\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.admin.resources.banner-resource.pages.edit-banner\"\n  \"component\" => \"App\\Filament\\Admin\\Resources\\BannerResource\\Pages\\EditBanner\"\n  \"id\" => \"c3sCEVO1suTtJnF7G2Kt\"\n]", "filament.livewire.global-search #xRgPprYF3mtMfDc1Cm3d": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"xRgPprYF3mtMfDc1Cm3d\"\n]", "filament.livewire.notifications #n87U88XR1JGTK477i0vp": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3707\n      #items: array:1 [\n        \"9d99339b-59f5-426d-9d7b-b6bb4bf6f66e\" => Filament\\Notifications\\Notification {#2698\n          #evaluationIdentifier: ? string\n          #view: \"filament-notifications::notification\"\n          #defaultView: null\n          #viewData: []\n          #viewIdentifier: \"notification\"\n          #safeViews: []\n          #isInline: false\n          #actions: []\n          #body: null\n          #date: null\n          #duration: 6000\n          #icon: \"heroicon-o-check-circle\"\n          #iconPosition: null\n          #iconSize: null\n          #iconColor: \"success\"\n          #id: \"9d99339b-59f5-426d-9d7b-b6bb4bf6f66e\"\n          #status: \"success\"\n          #title: \"Criado\"\n          #color: null\n          #defaultColor: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"n87U88XR1JGTK477i0vp\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FolWRu7ETNojsciEWiwvxgelKrYSvdZu8ZgtdZqn", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO", "_previous": "array:1 [\n  \"url\" => \"https://job.forradapg.com/ad-min-can-admin/banners/29/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "filament": "[]"}, "request": {"path_info": "/ad-min-can-admin/banners/29/edit", "status_code": "<pre class=sf-dump id=sf-dump-1062013731 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1062013731\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1800906129 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1800906129\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1065903012 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1065903012\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1959503019 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1453 characters\">_ga=GA1.1.1588727896.1727224672; _ga_ELXYT6N9JP=GS1.1.1727224672.1.1.1727224687.0.0.0; _fbp=fb.1.1727224779412.25516585183138564; _gcl_au=1.1.525778040.1727793989.463541494.1727794072.1727794684; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjIvZmJyQkNJR1lFZHY1VytoTFVYYnc9PSIsInZhbHVlIjoiR0hWWE9pT3phMG0ra0tXNEZ0TWNaUkQxUWY5ODMyNDZaQjAzYzM1a3FITWdybGw3QVozRXc3U1VZL3JOK0xwRDJQVTd1QVN6YmdZZWNIRDhDVXFtcDlhR0JlbWNtQUYydm1DSHhUYlFENmVqdWlOclRKVWlwLzU3a2d2WlpXTjdxaUlxbm1hK29TTk9aQVNraGxJN2x1aGwxYnNPbituMUhsb3BiQWhCV2JSTFZjQlVyOGx3NE55cm9tSnJXMzd6WlFIenRIOUxTRGEvR0wrMXpNaFZheVdZSWpkZ2ZRbFREZDh5bnZrL281bz0iLCJtYWMiOiJhNDI3ODAyM2ViYTU0MWZkMjZkZTIxMDNiZDZhOGQ1MDI1YjVlYWUxMWE1YzlmZDc2YmE4ZGQ1OGEzYjlhMWY3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlpkMHhRNFltdmU0OGRXMkF6aHB6cnc9PSIsInZhbHVlIjoieDJvSi95SkdScytJbHlzK0l1NUlCb0JiRlFSN3lyY29KczhtUEZFRTd5aTNkS005RWNUSVM5SkFNcW5RN3kvMXdRRHl6bGFETkxLSVFpSUhhUWJrTzExQUlBak9jRE1NRnZJQ290Z0ZSYUVlV3hyVkxLUDZJOGJzRW92V2F6WEIiLCJtYWMiOiI4NGFhMDNlMDc2MzRlZTE0Yzc1MjlkYmFkMTFhMGM3YjI5ZjZjYTY0Yzg4MDFlYjQ5NzFmNjNiOTBmMTI4NzkxIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6ImtsSWJLeHJveGJYTCthVUZFT0NyOHc9PSIsInZhbHVlIjoibitKRHBQeW9LRlFwTEtiNWJXTm1hdVJQUnByV2hkK2dXbTA5c0ExSVh4cllzdVNaRGtIT2tpVlJUbVozMVZDa2hva2lqRGZiVWhMZmNIQnkwV3ZuV2pFTkM4bHd3dGswODlURDl6TkVPVm1Sbk9qT1Axdyt6Mnh6RnBUOG95QUQiLCJtYWMiOiIxN2E1Y2Y5MTBmNTBkYmQzM2MxNjQxNGM5Mzc2NTVjOWE5OWJkZDRlNTExYjY4ZjZmMTlmMTNiYThjMzkwN2M0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">https://job.forradapg.com/ad-min-can-admin/banners/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1959503019\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1829113905 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_ELXYT6N9JP</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_fbp</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|k6ltzNsW4jF5VozMk8iT6IZZNvamyc7LmcHCDAU4zPjYzTFQWJoTdHwM2NjD|$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FolWRu7ETNojsciEWiwvxgelKrYSvdZu8ZgtdZqn</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DBzCPMApVq0pmypuI8QDLRsFwUspw4wKEldBwf9G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829113905\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 17:00:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkNQN2JSSWtuT1hkTVpLRjNNZmpDV2c9PSIsInZhbHVlIjoiK0psMGlxbzhtNmJ5TGRCaDJTcTJOK005TStBRXA2MWp5a0t1RW8zVExleXpuY2FIYnVHUk5CLzRsaGVzZVVtUzg1QkQyNUVTWHpEUkNJaGt5RzQ5Nllwditlb2ZRRGVYbGl0YVdNblJjbDYxMk9Uclo0N3l6RTBWV3N3a2llTUQiLCJtYWMiOiJlOThlZWIzN2E4Yzc4YmU4M2VkNzNkNDRmYjk2MTE2ZWFmMTBjMGM0MDIwNThmYmRkODExZjhjNDM1NTA5YTYzIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 19:00:18 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6ImlnTFpLQzN3VTRnV1d2ZWhTVnZ0VVE9PSIsInZhbHVlIjoibjArNk9NTUN6U2VvS3Z5WDFTVnYyMVZVOUZmMTdlTlZPRHNVamx3UlNGWXdHdmkyOGQvWEFyWW1zK2NZNnZMUTJpQ0V5TFNxbEN4d3A4dFEyaDBkZHVBNk4rdTNCUFdwekxHUFcvZi9DL1FPQWx6dGc3ZG5wcmVUUXY1RzVWemYiLCJtYWMiOiI2MjllNTk2OTYwMmNkYWYxMzUwZmRlMDJmNGZkZWU1MTNkMGY1OGIzNWIyYjU4NWQyNmEzYTZjYjMwMmUxZDQyIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 19:00:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkNQN2JSSWtuT1hkTVpLRjNNZmpDV2c9PSIsInZhbHVlIjoiK0psMGlxbzhtNmJ5TGRCaDJTcTJOK005TStBRXA2MWp5a0t1RW8zVExleXpuY2FIYnVHUk5CLzRsaGVzZVVtUzg1QkQyNUVTWHpEUkNJaGt5RzQ5Nllwditlb2ZRRGVYbGl0YVdNblJjbDYxMk9Uclo0N3l6RTBWV3N3a2llTUQiLCJtYWMiOiJlOThlZWIzN2E4Yzc4YmU4M2VkNzNkNDRmYjk2MTE2ZWFmMTBjMGM0MDIwNThmYmRkODExZjhjNDM1NTA5YTYzIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 19:00:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6ImlnTFpLQzN3VTRnV1d2ZWhTVnZ0VVE9PSIsInZhbHVlIjoibjArNk9NTUN6U2VvS3Z5WDFTVnYyMVZVOUZmMTdlTlZPRHNVamx3UlNGWXdHdmkyOGQvWEFyWW1zK2NZNnZMUTJpQ0V5TFNxbEN4d3A4dFEyaDBkZHVBNk4rdTNCUFdwekxHUFcvZi9DL1FPQWx6dGc3ZG5wcmVUUXY1RzVWemYiLCJtYWMiOiI2MjllNTk2OTYwMmNkYWYxMzUwZmRlMDJmNGZkZWU1MTNkMGY1OGIzNWIyYjU4NWQyNmEzYTZjYjMwMmUxZDQyIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 19:00:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FolWRu7ETNojsciEWiwvxgelKrYSvdZu8ZgtdZqn</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"58 characters\">https://job.forradapg.com/ad-min-can-admin/banners/29/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}