{"__meta": {"id": "Xc8848d6f88a0032cf26e938dd6f9b5ae", "datetime": "2024-11-28 12:25:50", "utime": 1732807550.245221, "method": "GET", "uri": "/api/profile/wallet/transfer-bonus-to-withdrawal-if-needed", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732807543.825097, "end": 1732807550.245239, "duration": 6.420141935348511, "duration_str": "6.42s", "measures": [{"label": "Booting", "start": 1732807543.825097, "relative_start": 0, "end": **********.195233, "relative_end": **********.195233, "duration": 2.370136022567749, "duration_str": "2.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.195254, "relative_start": 2.37015700340271, "end": 1732807550.245242, "relative_end": 3.0994415283203125e-06, "duration": 4.049988031387329, "duration_str": "4.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14295128, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/profile/wallet/transfer-bonus-to-withdrawal-if-needed", "middleware": "api, auth:api", "controller": "App\\Http\\Controllers\\Api\\Profile\\WalletController@transferBonusToWithdrawalIfNeeded", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=239\" onclick=\"\">app/Http/Controllers/Api/Profile/WalletController.php:239-259</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.014780000000000001, "accumulated_duration_str": "14.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `wallets` where `user_id` = ******** limit 1", "type": "query", "params": [], "bindings": [********], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 242}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.248478, "duration": 0.007690000000000001, "duration_str": "7.69ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:242", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=242", "ajax": false, "filename": "WalletController.php", "line": "242"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 52.03}, {"sql": "update `wallets` set `balance_withdrawal` = 0, `balance_bonus` = 0, `wallets`.`updated_at` = '2024-11-28 12:25:46' where `id` = 279", "type": "query", "params": [], "bindings": [0, 0, "2024-11-28 12:25:46", 279], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 254}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.2893138, "duration": 0.00709, "duration_str": "7.09ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:254", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 254}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=254", "ajax": false, "filename": "WalletController.php", "line": "254"}, "connection": "china15tema2", "explain": null, "start_percent": 52.03, "width_percent": 47.97}]}, "models": {"data": {"App\\Models\\Wallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/profile/wallet/transfer-bonus-to-withdrawal-if-needed", "status_code": "<pre class=sf-dump id=sf-dump-508564484 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-508564484\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1181844609 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1181844609\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1034932033 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1034932033\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-908297028 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IkdibkJzV0hqcU1VMHNUME9aS0pvQVE9PSIsInZhbHVlIjoiTHhTVDV3YVU5Q2VXM0lDSzNQRGtTaGdBekVSUU1obHFmNzJJcmdmcHUzK1BQNlgyUjB6N0tOU0RBY3d5eFpncEFlUWZyTm5iUnJ0RUh4S09QcWZXRnFBS2N5b1dZZm5JS3kzRmdRaVd6Wkl4OFNDK1k4WnFVSmpWdFRFQitUN08iLCJtYWMiOiI1NGJiYjA5MTk2NzNiZDE3OTg0Mzc1ZDVmZmUyZDc2MjU1Njg3YjU3NTRlN2E4NWViNDAxYjc1ZjdmYTM3Nzc3IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6Im1xSmh5d1pFUm10eHRvdjFoaStRbWc9PSIsInZhbHVlIjoib3RqVW4wS3VGKy8zcVdkalVGRWZrbzcxT0EzRmFkTFZ4WnFvZkxpWkJSajV4OW4wRnlRVkRKQURPdG9RNzVmQUs4RnFPUW92WlpzbkNmWkkvTmxySm9jUnZjMmhidVEyQ0Q1dDh1VFRWWk8wc2kyQlh6VU44bkJrdktKb0RuZjkiLCJtYWMiOiJlZDMxODQ2Mjg3YWJlNTAzZDM1OTg1YTNkMGMzZTkwMTM3N2I4YTFkODA5NmQ5OWYzZWEwYmUwMzBhMzZiMWZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdibkJzV0hqcU1VMHNUME9aS0pvQVE9PSIsInZhbHVlIjoiTHhTVDV3YVU5Q2VXM0lDSzNQRGtTaGdBekVSUU1obHFmNzJJcmdmcHUzK1BQNlgyUjB6N0tOU0RBY3d5eFpncEFlUWZyTm5iUnJ0RUh4S09QcWZXRnFBS2N5b1dZZm5JS3kzRmdRaVd6Wkl4OFNDK1k4WnFVSmpWdFRFQitUN08iLCJtYWMiOiI1NGJiYjA5MTk2NzNiZDE3OTg0Mzc1ZDVmZmUyZDc2MjU1Njg3YjU3NTRlN2E4NWViNDAxYjc1ZjdmYTM3Nzc3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZE0YVLZQjR9JyraSrLfdfqjJhqLh1JxweQcNpzyV</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-908297028\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-833133751 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdibkJzV0hqcU1VMHNUME9aS0pvQVE9PSIsInZhbHVlIjoiTHhTVDV3YVU5Q2VXM0lDSzNQRGtTaGdBekVSUU1obHFmNzJJcmdmcHUzK1BQNlgyUjB6N0tOU0RBY3d5eFpncEFlUWZyTm5iUnJ0RUh4S09QcWZXRnFBS2N5b1dZZm5JS3kzRmdRaVd6Wkl4OFNDK1k4WnFVSmpWdFRFQitUN08iLCJtYWMiOiI1NGJiYjA5MTk2NzNiZDE3OTg0Mzc1ZDVmZmUyZDc2MjU1Njg3YjU3NTRlN2E4NWViNDAxYjc1ZjdmYTM3Nzc3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im1xSmh5d1pFUm10eHRvdjFoaStRbWc9PSIsInZhbHVlIjoib3RqVW4wS3VGKy8zcVdkalVGRWZrbzcxT0EzRmFkTFZ4WnFvZkxpWkJSajV4OW4wRnlRVkRKQURPdG9RNzVmQUs4RnFPUW92WlpzbkNmWkkvTmxySm9jUnZjMmhidVEyQ0Q1dDh1VFRWWk8wc2kyQlh6VU44bkJrdktKb0RuZjkiLCJtYWMiOiJlZDMxODQ2Mjg3YWJlNTAzZDM1OTg1YTNkMGMzZTkwMTM3N2I4YTFkODA5NmQ5OWYzZWEwYmUwMzBhMzZiMWZmIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833133751\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1974555643 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 15:25:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974555643\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1568712152 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1568712152\", {\"maxDepth\":0})</script>\n"}}