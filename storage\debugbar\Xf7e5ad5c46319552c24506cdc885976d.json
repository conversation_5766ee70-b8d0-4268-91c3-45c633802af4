{"__meta": {"id": "Xf7e5ad5c46319552c24506cdc885976d", "datetime": "2024-11-28 10:30:55", "utime": 1732800655.023077, "method": "POST", "uri": "/livewire/update", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732800653.383412, "end": 1732800655.023117, "duration": 1.6397051811218262, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1732800653.383412, "relative_start": 0, "end": 1732800653.842727, "relative_end": 1732800653.842727, "duration": 0.45931506156921387, "duration_str": "459ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732800653.842737, "relative_start": 0.45932507514953613, "end": 1732800655.02312, "relative_end": 2.86102294921875e-06, "duration": 1.1803829669952393, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14321672, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.016209999999999995, "accumulated_duration_str": "16.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}], "start": **********.8229182, "duration": 0.01188, "duration_str": "11.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 73.288}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/User.php", "line": 176}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 34}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 183}], "start": **********.848593, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "china15tema2", "explain": null, "start_percent": 73.288, "width_percent": 3.393}, {"sql": "select sum(`balance_bet`) as aggregate from `ggr_games`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.879836, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:28", "source": {"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FWidgets%2FStatsOverview.php&line=28", "ajax": false, "filename": "StatsOverview.php", "line": "28"}, "connection": "china15tema2", "explain": null, "start_percent": 76.681, "width_percent": 5.552}, {"sql": "select sum(`balance_bet`) as aggregate from `ggr_games_drakon`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.893052, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:29", "source": {"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FWidgets%2FStatsOverview.php&line=29", "ajax": false, "filename": "StatsOverview.php", "line": "29"}, "connection": "china15tema2", "explain": null, "start_percent": 82.233, "width_percent": 2.159}, {"sql": "select sum(`balance_bet`) as aggregate from `ggr_games_fivers`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 30}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.9036782, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:30", "source": {"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FWidgets%2FStatsOverview.php&line=30", "ajax": false, "filename": "StatsOverview.php", "line": "30"}, "connection": "china15tema2", "explain": null, "start_percent": 84.392, "width_percent": 1.666}, {"sql": "select sum(`balance_bet`) as aggregate from `ggr_games_world_slots`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 31}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.914649, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:31", "source": {"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FWidgets%2FStatsOverview.php&line=31", "ajax": false, "filename": "StatsOverview.php", "line": "31"}, "connection": "china15tema2", "explain": null, "start_percent": 86.058, "width_percent": 2.344}, {"sql": "select sum(`balance_win`) as aggregate from `ggr_games`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 34}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.925818, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:34", "source": {"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FWidgets%2FStatsOverview.php&line=34", "ajax": false, "filename": "StatsOverview.php", "line": "34"}, "connection": "china15tema2", "explain": null, "start_percent": 88.402, "width_percent": 2.468}, {"sql": "select sum(`balance_win`) as aggregate from `ggr_games_drakon`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.937408, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:35", "source": {"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FWidgets%2FStatsOverview.php&line=35", "ajax": false, "filename": "StatsOverview.php", "line": "35"}, "connection": "china15tema2", "explain": null, "start_percent": 90.87, "width_percent": 2.468}, {"sql": "select sum(`balance_win`) as aggregate from `ggr_games_fivers`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.947674, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:36", "source": {"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FWidgets%2FStatsOverview.php&line=36", "ajax": false, "filename": "StatsOverview.php", "line": "36"}, "connection": "china15tema2", "explain": null, "start_percent": 93.337, "width_percent": 1.851}, {"sql": "select sum(`balance_win`) as aggregate from `ggr_games_world_slots`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.959952, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:37", "source": {"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FWidgets%2FStatsOverview.php&line=37", "ajax": false, "filename": "StatsOverview.php", "line": "37"}, "connection": "china15tema2", "explain": null, "start_percent": 95.188, "width_percent": 1.974}, {"sql": "select count(*) as aggregate from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.972527, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:43", "source": {"index": 19, "namespace": null, "name": "app/Filament/Admin/Widgets/StatsOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Widgets/StatsOverview.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FWidgets%2FStatsOverview.php&line=43", "ajax": false, "filename": "StatsOverview.php", "line": "43"}, "connection": "china15tema2", "explain": null, "start_percent": 97.162, "width_percent": 2.838}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"app.filament.admin.widgets.stats-overview #ESdY9cMTXt1UX53C3lIw": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.admin.widgets.stats-overview\"\n  \"component\" => \"App\\Filament\\Admin\\Widgets\\StatsOverview\"\n  \"id\" => \"ESdY9cMTXt1UX53C3lIw\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO", "_previous": "array:1 [\n  \"url\" => \"https://job.forradapg.com/ad-min-can-admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "DashboardAdmin_filters": "null", "filament": "[]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-1903940222 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1903940222\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-706020869 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-706020869\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-414801178 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"286 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;ESdY9cMTXt1UX53C3lIw&quot;,&quot;name&quot;:&quot;app.filament.admin.widgets.stats-overview&quot;,&quot;path&quot;:&quot;ad-min-can-admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;pt_BR&quot;},&quot;checksum&quot;:&quot;d6cb1ed06fe66972423bf911e4ca9bef055331d9c957c29f710173ef43c03758&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-414801178\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-654687033 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1453 characters\">_ga=GA1.1.1588727896.1727224672; _ga_ELXYT6N9JP=GS1.1.1727224672.1.1.1727224687.0.0.0; _fbp=fb.1.1727224779412.25516585183138564; _gcl_au=1.1.525778040.1727793989.463541494.1727794072.1727794684; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjIvZmJyQkNJR1lFZHY1VytoTFVYYnc9PSIsInZhbHVlIjoiR0hWWE9pT3phMG0ra0tXNEZ0TWNaUkQxUWY5ODMyNDZaQjAzYzM1a3FITWdybGw3QVozRXc3U1VZL3JOK0xwRDJQVTd1QVN6YmdZZWNIRDhDVXFtcDlhR0JlbWNtQUYydm1DSHhUYlFENmVqdWlOclRKVWlwLzU3a2d2WlpXTjdxaUlxbm1hK29TTk9aQVNraGxJN2x1aGwxYnNPbituMUhsb3BiQWhCV2JSTFZjQlVyOGx3NE55cm9tSnJXMzd6WlFIenRIOUxTRGEvR0wrMXpNaFZheVdZSWpkZ2ZRbFREZDh5bnZrL281bz0iLCJtYWMiOiJhNDI3ODAyM2ViYTU0MWZkMjZkZTIxMDNiZDZhOGQ1MDI1YjVlYWUxMWE1YzlmZDc2YmE4ZGQ1OGEzYjlhMWY3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImtMUFlXdXFZTElwenFCOWQwK3hqUXc9PSIsInZhbHVlIjoiMER5TzI3T0tuejNnL3g0OWFYWGNhcjRRR3l1dFI3czBWcVFuZUs1UTV1K0JXemhNYXNOdlIrUWhxL1NWbk5pbzlCMCtGRjFwQVhsSU8yTXBvSWpZZEJhSndKVjJFVEFVRmljMng0a3dsRDd4alpmMk5wRTI4MENnWGtjLzNJVFgiLCJtYWMiOiI4NzI2MGJhZjc0MGI4NjQ4ZDA3MWRjOTk1OWRhZmZmMWIxYjk4ZDk3MDI4NjAyNTljYjI4NmVkNWUzNGU1NjkyIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6Ik5NQ0x2TTR5ZlVuUjNYQlpqbCtEYUE9PSIsInZhbHVlIjoiYnRra2JPZTB5RWthUk10MUZMaWtta2pHM0ZIS0RCZVFEdVp5eUFpQUxTTXl2TW0xTDJFb2NPZ0l5eFhOSDIxZ3V2aHBFNHhDK212bS9kRm1YT2FCY1I2TmRxZGJqM2wxbXFrdHFJMGQrNytMa1JoS1NhSXJHb0VYNlNkeVZDSWUiLCJtYWMiOiI3YjBiNDY5OTk5OWFmZjc1ZGE0MDlkNGU4YTA1OWY1ZDc3ZmU4NTBjYTU5MjNhNWY2NGJkMjVjMGNjNmZjOTIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://job.forradapg.com/ad-min-can-admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">430</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-654687033\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-180488317 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_ELXYT6N9JP</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_fbp</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|k6ltzNsW4jF5VozMk8iT6IZZNvamyc7LmcHCDAU4zPjYzTFQWJoTdHwM2NjD|$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">o1TmPiJzyXN9mGIu9vfXbq7Y9cZbdaDWS7sUKgqp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-180488317\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-688411793 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 13:30:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkNhUXRJcTM3NVd2TkdvV1ZjMVNlWHc9PSIsInZhbHVlIjoiYkluRnlyZEtYN1FvT0lQUk5CY0NxbThiWlJZVCtBK3h6OVM2LzlVTENoeDFpSENKUC9tNXZva3ZtL0NVZ0JIVXdBRFpmSzNremwxQ1pQcUpFMTBKZWpYUEFab3U1MEFjcng0djZZVXZQZitmaGpEQkk3cjNGR2ZLUWVuUExudlIiLCJtYWMiOiIyMDI5Y2E2ODU1Njc5NDBjNmZiMzEzYmM2ZWFlYjlmNWNkZDEyNGZkZGIzNzlkNzkwMDc4NDQ0N2NhMzA3MmVhIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 15:30:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6IkdDZFN6NG5xZTRpemJSOXFsWXIrQ2c9PSIsInZhbHVlIjoieGpkUE9pVyt2UEk1YytSMWlZM0F4S01mSTYxY1JqV0pXbkFnUDFhbnNlOGlrZUxSY1lHRFRkbC9HVTNxeDF6K2djNUFKdXhFZ2xvTHVibEk3UlZORnVvbmNBZ0hPdGJBOW01K2Q0NkZhS2gxM3NNalJhUEZIMkhSSnlMd2VaK2giLCJtYWMiOiIyMGViYzJkNmY2NDhkMDFhYjA5MGUyYmRiOWIzNDY4MTBiMjVmNjA5MmNlODIzNTgzOWUyNDRkODc3MmVkNWM0IiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 15:30:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkNhUXRJcTM3NVd2TkdvV1ZjMVNlWHc9PSIsInZhbHVlIjoiYkluRnlyZEtYN1FvT0lQUk5CY0NxbThiWlJZVCtBK3h6OVM2LzlVTENoeDFpSENKUC9tNXZva3ZtL0NVZ0JIVXdBRFpmSzNremwxQ1pQcUpFMTBKZWpYUEFab3U1MEFjcng0djZZVXZQZitmaGpEQkk3cjNGR2ZLUWVuUExudlIiLCJtYWMiOiIyMDI5Y2E2ODU1Njc5NDBjNmZiMzEzYmM2ZWFlYjlmNWNkZDEyNGZkZGIzNzlkNzkwMDc4NDQ0N2NhMzA3MmVhIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 15:30:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6IkdDZFN6NG5xZTRpemJSOXFsWXIrQ2c9PSIsInZhbHVlIjoieGpkUE9pVyt2UEk1YytSMWlZM0F4S01mSTYxY1JqV0pXbkFnUDFhbnNlOGlrZUxSY1lHRFRkbC9HVTNxeDF6K2djNUFKdXhFZ2xvTHVibEk3UlZORnVvbmNBZ0hPdGJBOW01K2Q0NkZhS2gxM3NNalJhUEZIMkhSSnlMd2VaK2giLCJtYWMiOiIyMGViYzJkNmY2NDhkMDFhYjA5MGUyYmRiOWIzNDY4MTBiMjVmNjA5MmNlODIzNTgzOWUyNDRkODc3MmVkNWM0IiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 15:30:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-688411793\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1035644207 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://job.forradapg.com/ad-min-can-admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>DashboardAdmin_filters</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035644207\", {\"maxDepth\":0})</script>\n"}}