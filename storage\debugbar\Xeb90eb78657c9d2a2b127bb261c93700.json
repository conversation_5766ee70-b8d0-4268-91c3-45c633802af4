{"__meta": {"id": "Xeb90eb78657c9d2a2b127bb261c93700", "datetime": "2024-11-28 11:37:25", "utime": **********.463583, "method": "POST", "uri": "/livewire/update", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804644.061875, "end": **********.463602, "duration": 1.4017269611358643, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1732804644.061875, "relative_start": 0, "end": 1732804644.404898, "relative_end": 1732804644.404898, "duration": 0.34302282333374023, "duration_str": "343ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732804644.404917, "relative_start": 0.34304189682006836, "end": **********.463605, "relative_end": 2.86102294921875e-06, "duration": 1.0586879253387451, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14135104, "peak_usage_str": "13MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/update", "controller": "App\\Filament\\Admin\\Resources\\SettingResource\\Pages\\DefaultSetting@getFormUploadedFiles", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FConcerns%2FInteractsWithForms.php&line=183\" onclick=\"\">vendor/filament/forms/src/Concerns/InteractsWithForms.php:183-192</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03248, "accumulated_duration_str": "32.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}], "start": **********.3022108, "duration": 0.01728, "duration_str": "17.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 53.202}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/User.php", "line": 176}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 34}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 183}], "start": **********.333497, "duration": 0.00449, "duration_str": "4.49ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "china15tema2", "explain": null, "start_percent": 53.202, "width_percent": 13.824}, {"sql": "select * from `settings` where `settings`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 92}], "start": **********.353023, "duration": 0.01071, "duration_str": "10.71ms", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "china15tema2", "explain": null, "start_percent": 67.026, "width_percent": 32.974}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Setting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO", "_previous": "array:1 [\n  \"url\" => \"https://job.forradapg.com/ad-min-can-admin/settings\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "DashboardAdmin_filters": "null", "filament": "[]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-984599059 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-984599059\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1784159905 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1784159905\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-415614484 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"10736 characters\">{&quot;data&quot;:{&quot;record&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Setting&quot;,&quot;key&quot;:1,&quot;s&quot;:&quot;mdl&quot;}],&quot;data&quot;:[{&quot;id&quot;:1,&quot;software_name&quot;:&quot;Gold777Pg&quot;,&quot;software_description&quot;:&quot;O grupo Gold777Pg \\u00e9 uma das mais renomadas empresas internacionais de opera\\u00e7\\u00e3o de cassino online, oferecendo uma ampla variedade de jogos empolgantes, como jogos ao vivo com crupi\\u00ea real, slots, pesca, loterias, esportes e muito mais. Estamos autorizados e regulamentados pelo governo de Cura\\u00e7ao, operando com a licen\\u00e7a n\\u00famero Antillephone emitida para a 8048\\/JAZ. Passamos por todas as verifica\\u00e7\\u00f5es&quot;,&quot;software_favicon&quot;:[{&quot;2dc253eb-4e23-459f-a706-1acfe93d4cdc&quot;:&quot;uploads\\/ozUB6YkBH5vJ3Hh5LNivHHSAXDOQCBLoFf2j7v1G.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;software_logo_white&quot;:[{&quot;28e2a031-8259-48eb-bce3-ca7a2b0ff6f4&quot;:&quot;uploads\\/e8PrxGrSGExEvIzVlrxsV0AZdLSdoYbjiPNFrdYi.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;software_logo_black&quot;:[{&quot;928f4013-1af6-41fb-b44f-cb69572029ae&quot;:&quot;uploads\\/GPenHPxYqdFuUhOzn019Kq99XwV8dvpTPGS0MfT1.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;software_background&quot;:&quot;[]&quot;,&quot;currency_code&quot;:&quot;BRL&quot;,&quot;decimal_format&quot;:&quot;dot&quot;,&quot;currency_position&quot;:&quot;left&quot;,&quot;revshare_percentage&quot;:0,&quot;ngr_percent&quot;:1,&quot;soccer_percentage&quot;:30,&quot;prefix&quot;:&quot;R$&quot;,&quot;storage&quot;:&quot;local&quot;,&quot;initial_bonus&quot;:0,&quot;min_deposit&quot;:&quot;10.00&quot;,&quot;max_deposit&quot;:&quot;50000.00&quot;,&quot;min_withdrawal&quot;:&quot;15.00&quot;,&quot;max_withdrawal&quot;:&quot;1000.00&quot;,&quot;rollover&quot;:30,&quot;rollover_deposit&quot;:5,&quot;suitpay_is_enable&quot;:1,&quot;stripe_is_enable&quot;:0,&quot;bspay_is_enable&quot;:0,&quot;sharkpay_is_enable&quot;:0,&quot;turn_on_football&quot;:0,&quot;revshare_reverse&quot;:0,&quot;bonus_vip&quot;:0,&quot;activate_vip_bonus&quot;:0,&quot;maintenance_mode&quot;:0,&quot;withdrawal_limit&quot;:3,&quot;withdrawal_period&quot;:&quot;daily&quot;,&quot;disable_spin&quot;:0,&quot;perc_sub_lv1&quot;:1,&quot;perc_sub_lv2&quot;:2,&quot;perc_sub_lv3&quot;:3,&quot;disable_rollover&quot;:0,&quot;rollover_protection&quot;:1000,&quot;cpa_baseline&quot;:&quot;100.00&quot;,&quot;cpa_value&quot;:&quot;0.01&quot;,&quot;mercadopago_is_enable&quot;:0,&quot;digitopay_is_enable&quot;:0,&quot;default_gateway&quot;:&quot;suitpay&quot;,&quot;image_cassino_sidebar&quot;:&quot;uploads\\/9bWM9v18lbZDkhA5TzZaueDCN1y7tkbBWFKlrexR.png&quot;,&quot;image_favoritos_sidebar&quot;:[{&quot;266509d3-f217-475a-80a0-51473647743d&quot;:&quot;uploads\\/bTK9SB6NiLfRNpFIFqwI0vG7uxRYuXce9CEY7RRR.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_wallet_sidebar&quot;:[{&quot;1def8c8c-f08f-4fd2-a8a1-b1205e1e7346&quot;:&quot;uploads\\/UwzcWw79RARtmgYXOZCEwInVMUEGdPWUWZf7E8ig.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_suporte_sidebar&quot;:[{&quot;2f00ff9c-b73d-4f83-828f-763014624d6d&quot;:&quot;uploads\\/oQBdU3j3cCP4LLReOq31hDCvhwY6hyb3IstG5EVa.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_promotions_sidebar&quot;:[{&quot;685566ac-1a26-4cef-98bc-2a24affde7e0&quot;:&quot;uploads\\/HxmOJT0Jk7xbAVHw9zM133atBJrG56mevF4mCWzZ.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_indique_sidebar&quot;:[{&quot;8e86c845-a2dc-42db-afec-d4549a9aad7f&quot;:&quot;uploads\\/FOBC1W4pBz8tly5xWsajosHkzpC8lhk2oL7qWRiv.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_home_bottom&quot;:[{&quot;3011ac1b-eb8a-47c2-a84c-6e5ef5c7d447&quot;:&quot;uploads\\/tfBoffDrZFqQJsHM3WTJQTkvTrCl5qYAzCDB7oQ3.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_cassino_bottom&quot;:[{&quot;2ca50a0f-45d6-447f-9f15-21bf1cd27797&quot;:&quot;uploads\\/jQ1sYnlsntXw7iWb7h6YGciQuQQDwxgPi4xYWxlN.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_deposito_bottom&quot;:[{&quot;bb7143aa-2354-4311-a628-8910743726f1&quot;:&quot;uploads\\/h20H9WLsdjUeXar52PNWD9q13C14K2YcFXy4Trx1.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_convidar_bottom&quot;:[{&quot;a4726488-f434-472d-ae33-dd23d181590f&quot;:&quot;uploads\\/X2ZnEIZySFXoPVOajOravdk6vR3l8tBkVpmLcLyk.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_wallet_bottom&quot;:[{&quot;e50e67ae-cb35-4926-9b29-a1e56f2bfe49&quot;:&quot;uploads\\/siz2Qxm3sf9fStOeze53zugSMmNWeSOZxtLWLnoS.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_user_nav&quot;:&quot;uploads\\/2gG8djPHVaT6XIJp3b6eP8BEPsBnXr9s6qaSwlKt.png&quot;,&quot;image_home_sidebar&quot;:&quot;uploads\\/iD1OmGAmetuS2vGWLn0gjglv6KhYnxeHm5tbqpxp.png&quot;,&quot;image_menu_nav&quot;:[{&quot;e0734303-3164-4624-8f38-9d84c77fa90b&quot;:&quot;uploads\\/W4fPdZy0VIPJMPkvH7E8qDs8I2FdIyDGHKeYQHvZ.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;message_home_page&quot;:&quot;Descubra a maior rede de cassinos do mundo, onde pr\\u00eamios abundantes e b\\u00f4nus incr\\u00edveis esperam por voc\\u00ea, al\\u00e9m de um exclusivo sistema de ba\\u00fa repleto de surpresas!&quot;,&quot;valor_por_bau&quot;:&quot;10&quot;,&quot;deposito_minimo_bau&quot;:&quot;40&quot;,&quot;cirus_baseline&quot;:&quot;100.00&quot;,&quot;cirus_aposta&quot;:&quot;100.00&quot;,&quot;cirus_valor&quot;:&quot;0.01&quot;,&quot;icon_bt_1&quot;:[{&quot;e4f8f3e2-d563-4c00-b065-a81756dab912&quot;:&quot;uploads\\/eWpQcgZGkbwrhrNfpW1BepDOrXEWzuh5BNV9HG4z.avif&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_bt_2&quot;:[{&quot;cd4dd5ca-c313-4832-b51d-7b2397a49757&quot;:&quot;uploads\\/P1XArH6s5KrOeeMcWgPRA8uQVQTme3zUaXSWI9pV.avif&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_bt_3&quot;:[{&quot;add1ac40-aed9-4ca4-9eb7-dce7c58e5327&quot;:&quot;uploads\\/f9T4FvH6AErXT6r6KLEOONqIz2DdjARvrEX6CXVL.avif&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_bt_4&quot;:[{&quot;c4c78471-1303-4195-ae68-ba7de734325d&quot;:&quot;uploads\\/r5KZk1fSozvUAqydbCMjTFv7P8wVoxFS6RBoshlR.avif&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_bt_5&quot;:[{&quot;fcffe2dd-7277-4d5f-96b2-8c4d4dc38b25&quot;:&quot;uploads\\/oDZr05OwyFwjRpXRVn2xXMvxGayW9lQwpaaHp5b9.avif&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;name_bt_1&quot;:&quot;Agente&quot;,&quot;name_bt_2&quot;:&quot;Promo\\u00e7\\u00e3o&quot;,&quot;name_bt_3&quot;:&quot;Suporte &quot;,&quot;name_bt_4&quot;:&quot;VIP&quot;,&quot;img_bg_1&quot;:[{&quot;6e8fda43-52b0-4810-b06f-c295ad235afd&quot;:&quot;uploads\\/EJUweLmD5X1DnsKD5fzZ7g0vkiNfajvHrTReSjEo.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_bt_6&quot;:[{&quot;ec81a4ed-2b01-4f49-bd0c-20f252ffb4e8&quot;:&quot;uploads\\/faQwqq6CGAXiINRMJqaa5CE24Coc0JuRudyWqR3x.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_bt_7&quot;:[{&quot;92d5d5f8-1c2e-423a-8c9d-20d36705ffb8&quot;:&quot;uploads\\/jW0GzrSVlfLHqNvpcIZs7a1DH3w3WFgDK16DZfNz.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_bt_8&quot;:[{&quot;d2ed2b5c-5db3-4f2d-b25a-6390c07a7f70&quot;:&quot;uploads\\/xgqf3MLF4tmK0WyVLfLsXx6i8KW5yCF4lGHCAQDH.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_1&quot;:[{&quot;20dc9e3a-ee52-4acc-8554-910e7f105e02&quot;:&quot;uploads\\/HodjXhcXseRrKQ7ScX2PIDn8aVLFYnKaQV1C9Ksu.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_2&quot;:[{&quot;808c0870-9f56-4573-8a37-8bba12cc85b1&quot;:&quot;uploads\\/waA5HADJBb5krbf7QgIZNbDPL2HeFNiihvfa2G5j.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_3&quot;:[{&quot;6fcfe242-2bea-4a5c-a37d-3faa845d4fd7&quot;:&quot;uploads\\/BB7nc6zxKsVHa1EA41x3HXMv9lV7Dz6fNPKDEwFg.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_4&quot;:[{&quot;8c93f383-6d90-4d73-9a87-15ff8916a6e6&quot;:&quot;uploads\\/6sCsprPnK3GyxAsqsVOqGE2v3zidz545vfK6l02p.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_5&quot;:[{&quot;ef64bcfc-2de0-4fa7-87b5-327e8308073b&quot;:&quot;uploads\\/et1miv404wB8ixnpCcwNsh83pM9EXAgZQYmYUNtR.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;saldo_ini&quot;:&quot;0.00&quot;,&quot;ezzebank_is_enable&quot;:0,&quot;modal_pop_up&quot;:&quot;&lt;h2&gt;&lt;strong&gt;\\ud83d\\udd25B\\u00f4nus de Afiliado - Siga a P\\u00e1gina Inicial\\ud83d\\udd25&lt;\\/strong&gt;&lt;\\/h2&gt;&lt;p&gt;\\ud83d\\udc4dSite oficial:\\ud83d\\udc49 &lt;a href=\\&quot;https:\\/\\/democassino.site\\&quot;&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;www.cgx777.&lt;\\/span&gt;&lt;\\/a&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;com&lt;\\/span&gt;&lt;br&gt;\\u2708\\ufe0fSiga o canal do Telegram: \\ud83d\\udc49 &lt;a href=\\&quot;https:\\/\\/democassino.site\\&quot;&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;a&lt;\\/span&gt;&lt;\\/a&gt;&lt;a href=\\&quot;https:\\/\\/democassino.site\\/\\&quot;&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;cgx777.&lt;\\/span&gt;&lt;\\/a&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;com&lt;\\/span&gt;&lt;br&gt;\\ud83d\\udcd2Clique na p\\u00e1gina inicial do Facebook:\\ud83d\\udc49 &lt;a href=\\&quot;https:\\/\\/democassino.site\\&quot;&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;a&lt;\\/span&gt;&lt;\\/a&gt;&lt;a href=\\&quot;https:\\/\\/democassino.site\\/\\&quot;&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;cgx777.&lt;\\/span&gt;&lt;\\/a&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;com&lt;\\/span&gt;&lt;br&gt;\\ud83d\\udcf2Baixe o site oficial do APP:\\ud83d\\udc49 &lt;a href=\\&quot;https:\\/\\/democassino.site\\&quot;&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;app.&lt;\\/span&gt;&lt;\\/a&gt;&lt;a href=\\&quot;https:\\/\\/democassino.site\\/\\&quot;&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;cgx777.&lt;\\/span&gt;&lt;\\/a&gt;&lt;span style=\\&quot;text-decoration: underline;\\&quot;&gt;com&lt;\\/span&gt;&lt;br&gt;\\ud83c\\udf81Agente-Ganhe facilmente R$100.000 por dia&lt;br&gt;\\ud83e\\udde7Basta se cadastrar como membro CGX777, e encaminhar o link proxy com um clique; Recomende 1 pessoa recompensa: R$15; recomende 10 pessoas recompensa: R$150; at\\u00e9 &lt;strong&gt;R$150.000&lt;\\/strong&gt;. Quanto mais amigos voc\\u00ea compartilhar, mais recompensas voc\\u00ea ganha.&lt;br&gt;\\u2705Passos: registre-se como membro\\/clique (meu)\\/obtenha o link\\/encaminhamento com um clique\\/conclua a tarefa\\/clique para receber - o b\\u00f4nus chega&lt;\\/p&gt;&quot;,&quot;img_modal_pop&quot;:[{&quot;92475731-03dc-4574-a46f-1d1ed237c76c&quot;:&quot;uploads\\/Ushcs9qiLY6vQlDIGCvOibL6b6qv0U5h0kQIKTHz.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;modal_active&quot;:false,&quot;icon_wt_6&quot;:[{&quot;16f40687-5a8f-438a-8e9e-92b0995092b1&quot;:&quot;uploads\\/O0xA9mgVr9qF6YitB6OU1s9hILHYpxhhe09V0RPp.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_7&quot;:[{&quot;8004b943-00d5-4e63-bcd3-6312ce6386f5&quot;:&quot;uploads\\/uxPIg7WGu4HkZSPZ4sxrpEqxPoLGnDdPaiUmU0AS.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_8&quot;:[{&quot;3da6d755-d647-4974-9c3c-4b732366b72f&quot;:&quot;uploads\\/8hRYr6rptQfUr4Tfs4hu2tY1b23fWeawtbQ3CojM.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;software_loading&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;image_home_bottom_hover&quot;:[{&quot;89c0dabf-ac66-4429-ba97-e5249756c540&quot;:&quot;uploads\\/VWaLG1fZB9o5JbUPTKvvLHJMjfGoLfNj9m64iuQB.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_cassino_bottom_hover&quot;:[{&quot;173bcade-aa8e-4d0e-a329-318019677a3b&quot;:&quot;uploads\\/lBELgu4VLMbbS6pBXWro3uUQugtS1c1D12pNP0lf.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_deposito_bottom_hover&quot;:[{&quot;ad5f7040-4455-4357-9728-e7258b810377&quot;:&quot;uploads\\/nBb90g1ARw7oySS13ZOinxmKaxL1ul5rXyGE8AIW.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_convidar_bottom_hover&quot;:[{&quot;7071f170-f2a3-49fc-b602-a220105aedb2&quot;:&quot;uploads\\/LaBQ1lwiL9PawxEMgWC9jzWbF1Jhg62Hods9wMOB.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;image_wallet_bottom_hover&quot;:[{&quot;507c566d-45a6-465b-ac10-7e63a1f90c0b&quot;:&quot;uploads\\/S1wqBLLXb0WQzzq59wMiQNbpNP0XZPyVvH5u13bm.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_9&quot;:[{&quot;4946fe19-114a-4d6e-a089-b0df4ef9ea73&quot;:&quot;uploads\\/0HuA5qD9TWhtAaPwzXadGEA66WIvP0UA6kuVf9YV.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;background_perfil_top&quot;:[{&quot;ba735a10-b367-4b3d-a94b-75c9986d598e&quot;:&quot;uploads\\/RIUu9JCj8xCrIxJBNbpTS7Fzo97oTt1UOGaqMlL0.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;sub_background_perfil_top&quot;:[{&quot;539e990d-d4b9-403b-9a4f-1ac9f00cda6f&quot;:&quot;uploads\\/vCundEuA5eFsWPfkEtXwamYxJSorisd2aIuWF5FD.webp&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_10&quot;:[{&quot;10720d1d-ecd3-4b33-8a23-a98a20b3b15b&quot;:&quot;uploads\\/vJ7q12GQ2NSN0mmcqPBYELuEtKmC3UltqCx5zLMR.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;collum_games&quot;:&quot;3&quot;,&quot;disable_rollover_cadastro&quot;:0,&quot;rollover_cadastro&quot;:10,&quot;disable_deposit_min&quot;:0,&quot;deposit_min_saque&quot;:&quot;0.00&quot;,&quot;icon_nav_bottom_left&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_nav_bottom_right&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_bottom_right&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_bottom_left&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_11&quot;:[{&quot;4e47335f-dfe4-48e0-9d37-67b74409f31d&quot;:&quot;uploads\\/TpazO0EnwCqi26rxCpH6C0f35dg4EAo1LYjGTSPm.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;icon_wt_12&quot;:[{&quot;bffac8c7-f3dc-4a41-96d5-2406426f5024&quot;:&quot;uploads\\/KjeD07HUxHXXLEFbtbNh5PUJhsVTAnkRsjqIzS8l.png&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;approval_password_save&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;DnSKzFWqnjpCjpOHPgUr&quot;,&quot;name&quot;:&quot;app.filament.admin.resources.setting-resource.pages.default-setting&quot;,&quot;path&quot;:&quot;ad-min-can-admin\\/settings&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;pt_BR&quot;},&quot;checksum&quot;:&quot;43ebf99c4e07ca0d966f55de99cc796e2af149c0995248746c024e3929cd004c&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:48</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_wt_3</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">data.image_indique_sidebar</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">data.img_modal_pop</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_wt_8</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">data.image_cassino_bottom</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_wt_9</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">data.image_favoritos_sidebar</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_wt_6</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_bt_7</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">data.img_bg_1</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">data.image_convidar_bottom_hover</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">data.image_wallet_sidebar</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">data.icon_wt_11</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">data.icon_bottom_left</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">data.icon_nav_bottom_left</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_bt_3</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">data.image_menu_nav</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">data.image_deposito_bottom_hover</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">data.image_convidar_bottom</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_bt_5</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">data.icon_bottom_right</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">data.software_loading</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">data.software_logo_black</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_wt_4</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">data.image_wallet_bottom</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_bt_6</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_bt_2</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_bt_4</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">data.image_cassino_bottom_hover</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_wt_7</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">data.icon_nav_bottom_right</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_wt_1</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">data.background_perfil_top</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_bt_8</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">data.image_deposito_bottom</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">data.image_promotions_sidebar</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">data.software_logo_white</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">data.image_wallet_bottom_hover</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_bt_1</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">data.image_home_bottom</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_wt_2</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">data.icon_wt_10</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">data.sub_background_perfil_top</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">data.icon_wt_5</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">data.image_suporte_sidebar</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">data.software_favicon</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">data.image_home_bottom_hover</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">data.icon_wt_12</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-415614484\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-749088704 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1453 characters\">_ga=GA1.1.1588727896.1727224672; _ga_ELXYT6N9JP=GS1.1.1727224672.1.1.1727224687.0.0.0; _fbp=fb.1.1727224779412.25516585183138564; _gcl_au=1.1.525778040.1727793989.463541494.1727794072.1727794684; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjIvZmJyQkNJR1lFZHY1VytoTFVYYnc9PSIsInZhbHVlIjoiR0hWWE9pT3phMG0ra0tXNEZ0TWNaUkQxUWY5ODMyNDZaQjAzYzM1a3FITWdybGw3QVozRXc3U1VZL3JOK0xwRDJQVTd1QVN6YmdZZWNIRDhDVXFtcDlhR0JlbWNtQUYydm1DSHhUYlFENmVqdWlOclRKVWlwLzU3a2d2WlpXTjdxaUlxbm1hK29TTk9aQVNraGxJN2x1aGwxYnNPbituMUhsb3BiQWhCV2JSTFZjQlVyOGx3NE55cm9tSnJXMzd6WlFIenRIOUxTRGEvR0wrMXpNaFZheVdZSWpkZ2ZRbFREZDh5bnZrL281bz0iLCJtYWMiOiJhNDI3ODAyM2ViYTU0MWZkMjZkZTIxMDNiZDZhOGQ1MDI1YjVlYWUxMWE1YzlmZDc2YmE4ZGQ1OGEzYjlhMWY3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlhGeC9TeC9pZmVVUDdqZUptdDNGdFE9PSIsInZhbHVlIjoiREkrYnplRGFlZGZkallYMC9TVCtUb25uYWpYekFRSVRRcnFoaWxNUTBrLzMyNHUxeHY0T1VaMHlCaS9aUXJxa2J0eVkrRXR1V1NQVWNBaWwzcjl1YkJMQzI5ejdmWTlsaHhZOCtXemE3aitRRjVmb2FxTWc4L3l3M0o4dkh6ZFMiLCJtYWMiOiI1OGQwYTNlZmNmYTEwMWU4NWJhMzljNzhlYzJkZDgyNzUzYzQ2ZTUzNWQ4Zjc3N2FjNWE4YzY4ZmRiMjE3ZDVlIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6ImZ0ZkFya0ZXbkpZUGtIRjVXazltbVE9PSIsInZhbHVlIjoiR1Frckc4N21uV25XdUVDNWtuY3o0L0hNZXJJTUZVTlFpcU9EVWV0NnlmQVVCb2R3ZXRNNlc4aGxzOUJBZ0R4ZXVRUmpFUHFITkdzdTM4UjdOVEw1Y2YzUU5YYzY5eXpwbW1MUUE1TUhRWG10RlY5NzdiZjN1UWYzSG8xZnlRakkiLCJtYWMiOiI2YWQ4NjUzY2U3ZjdiNzc1ZDdlMjJkNDk1ODE2Y2RlZjVhMzE0ODBkZTExYTM3MzYxNGVkY2QwMTIxMzAzZTIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://job.forradapg.com/ad-min-can-admin/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">15603</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-749088704\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-332203523 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_ELXYT6N9JP</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_fbp</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|k6ltzNsW4jF5VozMk8iT6IZZNvamyc7LmcHCDAU4zPjYzTFQWJoTdHwM2NjD|$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">o1TmPiJzyXN9mGIu9vfXbq7Y9cZbdaDWS7sUKgqp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-332203523\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1386518193 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:37:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InoycWtzaDcvVllJU2plSFB6M1QwQVE9PSIsInZhbHVlIjoiRjd5emZJQi9mamJNTGtWT2QwSkF2THRBYzNsNnN2RTZCc0liM1NuMlpEMS9RV1AwQkRSbWRyYlQ3ODdubFk0Qi96cEVIOFBGMzB0Mk9waUo0NmFjYm5aQWVxV0JkYkl2V2FWZ2pTWjEzdjhxT2h0R0xMUEt5WGRGTDlzMi9NbnIiLCJtYWMiOiI5YzU4OWU5MmFlZmZjZTlhOWEzZTlmODIyNzBlNjU4MDE0MGEzYmVkMTUyMTZmZTVlNDBhZjI5YmViZjRjOTk3IiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 16:37:25 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6IlgzdTdlRmhDSFoyNkNwczZTUGxDVUE9PSIsInZhbHVlIjoiM0lrUmQ0dk9PeTVvYmZhNjlxdUNQb200dUM3MGFaQmVFQnROcFozSnNrQVUvWVFFWElEWHYxaEwwWE1sdGlDWFJ4YWJjRWZkdk90ZWcwOHozektTbmd3VVkwZzR0eTdmUjU4QktyRDFJQU9UWklxMjZkWDN0OTVuNndMeWwvbjciLCJtYWMiOiIxZjJiMWNlM2FiZDBjNTQ5MTJlZTQ5YjU4OWI2NWQwMjljNTU1YzZkYjhhNzQ0ZjJjMTRiNGY2NDRhYWNhYThiIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 16:37:25 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InoycWtzaDcvVllJU2plSFB6M1QwQVE9PSIsInZhbHVlIjoiRjd5emZJQi9mamJNTGtWT2QwSkF2THRBYzNsNnN2RTZCc0liM1NuMlpEMS9RV1AwQkRSbWRyYlQ3ODdubFk0Qi96cEVIOFBGMzB0Mk9waUo0NmFjYm5aQWVxV0JkYkl2V2FWZ2pTWjEzdjhxT2h0R0xMUEt5WGRGTDlzMi9NbnIiLCJtYWMiOiI5YzU4OWU5MmFlZmZjZTlhOWEzZTlmODIyNzBlNjU4MDE0MGEzYmVkMTUyMTZmZTVlNDBhZjI5YmViZjRjOTk3IiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 16:37:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6IlgzdTdlRmhDSFoyNkNwczZTUGxDVUE9PSIsInZhbHVlIjoiM0lrUmQ0dk9PeTVvYmZhNjlxdUNQb200dUM3MGFaQmVFQnROcFozSnNrQVUvWVFFWElEWHYxaEwwWE1sdGlDWFJ4YWJjRWZkdk90ZWcwOHozektTbmd3VVkwZzR0eTdmUjU4QktyRDFJQU9UWklxMjZkWDN0OTVuNndMeWwvbjciLCJtYWMiOiIxZjJiMWNlM2FiZDBjNTQ5MTJlZTQ5YjU4OWI2NWQwMjljNTU1YzZkYjhhNzQ0ZjJjMTRiNGY2NDRhYWNhYThiIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 16:37:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1386518193\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-637661938 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"51 characters\">https://job.forradapg.com/ad-min-can-admin/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>DashboardAdmin_filters</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637661938\", {\"maxDepth\":0})</script>\n"}}