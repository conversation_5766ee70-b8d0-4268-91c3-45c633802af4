{"__meta": {"id": "Xf0aa5c4f6b8caf46d1f479efef513a08", "datetime": "2024-11-28 11:31:30", "utime": 1732804290.372466, "method": "GET", "uri": "/api/settings/data", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804288.236842, "end": 1732804290.372487, "duration": 2.1356451511383057, "duration_str": "2.14s", "measures": [{"label": "Booting", "start": 1732804288.236842, "relative_start": 0, "end": 1732804288.621084, "relative_end": 1732804288.621084, "duration": 0.38424205780029297, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732804288.621098, "relative_start": 0.38425612449645996, "end": 1732804290.372491, "relative_end": 3.814697265625e-06, "duration": 1.7513928413391113, "duration_str": "1.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14189352, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/settings/data", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Settings\\SettingController@index", "namespace": null, "prefix": "api/settings", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FSettings%2FSettingController.php&line=13\" onclick=\"\">app/Http/Controllers/Api/Settings/SettingController.php:13-16</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/settings/data", "status_code": "<pre class=sf-dump id=sf-dump-726786000 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-726786000\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1796967331 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1796967331\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-19939813 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-19939813\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-262180958 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6Im9GVlhWUWhIR0luVnhEVEFlRzIzamc9PSIsInZhbHVlIjoiNnM4N0RBVnFCeFdPbFFTOXlWZW5aOXN1ODBxTnduYUF3WU9yQitaK2M1MERFT0pHc2NCREtvcE9VVCtVN2krNWh2STBGbEhraTNkdnI2TUxHR05McTVWb1I0QlpPcDJPWjhCeWpsZHNVcVNxRG1IS3crQ2VqSTFCY2kxWVNORUkiLCJtYWMiOiJmOWJmMjk5ZTBjMTA2MDc2ZTA1ZWRkMTNlYmM4NzJlNDA5ZDQxY2M5ODljMzQ3ZDYzZGZmYmNiZGU0YWQ2NDNhIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6InZwcEdpdmh0RDFXcFIrdmFkWEk3SWc9PSIsInZhbHVlIjoicTRCZTVwRGo1a1BlMFZyUWxmRHBXaGRFbTBudHB2NFZLdlBLR05uWFdEQmhqODhwUWdFK2V2OFFLTlRzWTY1WmFXVGp6bjA5MEcwMVo5TVBUYzQ5SzZoemNIT1krczNTc09WN0tWaVQyYjZiZkZqeis4SGNDTUxDVm54dmVCYTciLCJtYWMiOiI2M2NjMWIxOTEyMjhjOGM0NzNjNGM0MzZkNDU2ODlhMzFkYmRjOWRjOWYyZWY2NDE1ZGJlNTgxM2E1Y2I2NDM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://job.forradapg.com/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im9GVlhWUWhIR0luVnhEVEFlRzIzamc9PSIsInZhbHVlIjoiNnM4N0RBVnFCeFdPbFFTOXlWZW5aOXN1ODBxTnduYUF3WU9yQitaK2M1MERFT0pHc2NCREtvcE9VVCtVN2krNWh2STBGbEhraTNkdnI2TUxHR05McTVWb1I0QlpPcDJPWjhCeWpsZHNVcVNxRG1IS3crQ2VqSTFCY2kxWVNORUkiLCJtYWMiOiJmOWJmMjk5ZTBjMTA2MDc2ZTA1ZWRkMTNlYmM4NzJlNDA5ZDQxY2M5ODljMzQ3ZDYzZGZmYmNiZGU0YWQ2NDNhIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZxogPn2IBCCd0wbASYCv5voRQyu5lNXDFz0pAZq2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-262180958\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1114386620 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im9GVlhWUWhIR0luVnhEVEFlRzIzamc9PSIsInZhbHVlIjoiNnM4N0RBVnFCeFdPbFFTOXlWZW5aOXN1ODBxTnduYUF3WU9yQitaK2M1MERFT0pHc2NCREtvcE9VVCtVN2krNWh2STBGbEhraTNkdnI2TUxHR05McTVWb1I0QlpPcDJPWjhCeWpsZHNVcVNxRG1IS3crQ2VqSTFCY2kxWVNORUkiLCJtYWMiOiJmOWJmMjk5ZTBjMTA2MDc2ZTA1ZWRkMTNlYmM4NzJlNDA5ZDQxY2M5ODljMzQ3ZDYzZGZmYmNiZGU0YWQ2NDNhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZwcEdpdmh0RDFXcFIrdmFkWEk3SWc9PSIsInZhbHVlIjoicTRCZTVwRGo1a1BlMFZyUWxmRHBXaGRFbTBudHB2NFZLdlBLR05uWFdEQmhqODhwUWdFK2V2OFFLTlRzWTY1WmFXVGp6bjA5MEcwMVo5TVBUYzQ5SzZoemNIT1krczNTc09WN0tWaVQyYjZiZkZqeis4SGNDTUxDVm54dmVCYTciLCJtYWMiOiI2M2NjMWIxOTEyMjhjOGM0NzNjNGM0MzZkNDU2ODlhMzFkYmRjOWRjOWYyZWY2NDE1ZGJlNTgxM2E1Y2I2NDM2IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114386620\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1387742255 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:31:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387742255\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1635268961 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1635268961\", {\"maxDepth\":0})</script>\n"}}