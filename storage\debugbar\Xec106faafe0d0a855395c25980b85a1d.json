{"__meta": {"id": "Xec106faafe0d0a855395c25980b85a1d", "datetime": "2024-11-28 11:17:51", "utime": 1732803471.239138, "method": "GET", "uri": "/api/settings/data", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732803469.930123, "end": 1732803471.239161, "duration": 1.3090379238128662, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1732803469.930123, "relative_start": 0, "end": 1732803470.321345, "relative_end": 1732803470.321345, "duration": 0.3912220001220703, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732803470.32136, "relative_start": 0.3912370204925537, "end": 1732803471.239164, "relative_end": 3.0994415283203125e-06, "duration": 0.9178040027618408, "duration_str": "918ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14189400, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/settings/data", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Settings\\SettingController@index", "namespace": null, "prefix": "api/settings", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FSettings%2FSettingController.php&line=13\" onclick=\"\">app/Http/Controllers/Api/Settings/SettingController.php:13-16</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/settings/data", "status_code": "<pre class=sf-dump id=sf-dump-1692037711 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1692037711\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1221982488 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1221982488\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-142326067 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-142326067\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2037328077 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6ImdrWnFXbklyQWZSVHNTNW03ZTlOaWc9PSIsInZhbHVlIjoiVVlUYWxyaisxYU5PaXVrVU1URUhBZUpiNFg1S2p3clM4SFdRTm5SOG9oSmxrdkdhTXg3RHYzOVpZd1ZqL2RDQjBTOXZxVVNlTU5FWGVHakFLb0xBRlM5dVJZNmhJai9iTEFOWnNZZXNDUzQ2YndoM1dkaTYyd2NvM2JnUFN4bG0iLCJtYWMiOiI2YzdlZjAxNDJkYzJjZDIyNDgyMWFiNDU1ZGE4MjEwNDI4M2I1Njk0ZGMwOTg5MDA5OWVlYjVjY2ZiZmFlMmRmIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6ImkrRkZTUjd0QWlvQitGdUxydGdDbGc9PSIsInZhbHVlIjoiUmxFc2lMdnEweGlUaXUyUy8vT01JNVRxRjU0eW5wOGFYekRXNE5OUVZzcmZwOEFGRGhlaHR0bkxaRGxkOGt4T1JWQ1ZEY1MrdStvbmpZODkxVmJISjhJQk85SStkeG1iWTBxdkpIeHMrU0JEOXhjcVErRXErQStsYTFYZDNBWXMiLCJtYWMiOiIwYzM5YjBkZGQ3ODE3NmEzZGExMzM3NjY1NmU0MmJkZWIwOGNhNDI0ZWU1YWI2MDU5Y2M1Mzk4NDUxZmQyYWIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImdrWnFXbklyQWZSVHNTNW03ZTlOaWc9PSIsInZhbHVlIjoiVVlUYWxyaisxYU5PaXVrVU1URUhBZUpiNFg1S2p3clM4SFdRTm5SOG9oSmxrdkdhTXg3RHYzOVpZd1ZqL2RDQjBTOXZxVVNlTU5FWGVHakFLb0xBRlM5dVJZNmhJai9iTEFOWnNZZXNDUzQ2YndoM1dkaTYyd2NvM2JnUFN4bG0iLCJtYWMiOiI2YzdlZjAxNDJkYzJjZDIyNDgyMWFiNDU1ZGE4MjEwNDI4M2I1Njk0ZGMwOTg5MDA5OWVlYjVjY2ZiZmFlMmRmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">fatnAQB1TjzrXGv4ixSN3n4Vtmgn62kWhCZCWs10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2037328077\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-537911267 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImdrWnFXbklyQWZSVHNTNW03ZTlOaWc9PSIsInZhbHVlIjoiVVlUYWxyaisxYU5PaXVrVU1URUhBZUpiNFg1S2p3clM4SFdRTm5SOG9oSmxrdkdhTXg3RHYzOVpZd1ZqL2RDQjBTOXZxVVNlTU5FWGVHakFLb0xBRlM5dVJZNmhJai9iTEFOWnNZZXNDUzQ2YndoM1dkaTYyd2NvM2JnUFN4bG0iLCJtYWMiOiI2YzdlZjAxNDJkYzJjZDIyNDgyMWFiNDU1ZGE4MjEwNDI4M2I1Njk0ZGMwOTg5MDA5OWVlYjVjY2ZiZmFlMmRmIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImkrRkZTUjd0QWlvQitGdUxydGdDbGc9PSIsInZhbHVlIjoiUmxFc2lMdnEweGlUaXUyUy8vT01JNVRxRjU0eW5wOGFYekRXNE5OUVZzcmZwOEFGRGhlaHR0bkxaRGxkOGt4T1JWQ1ZEY1MrdStvbmpZODkxVmJISjhJQk85SStkeG1iWTBxdkpIeHMrU0JEOXhjcVErRXErQStsYTFYZDNBWXMiLCJtYWMiOiIwYzM5YjBkZGQ3ODE3NmEzZGExMzM3NjY1NmU0MmJkZWIwOGNhNDI0ZWU1YWI2MDU5Y2M1Mzk4NDUxZmQyYWIwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537911267\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-274987105 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:17:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274987105\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1290277831 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1290277831\", {\"maxDepth\":0})</script>\n"}}