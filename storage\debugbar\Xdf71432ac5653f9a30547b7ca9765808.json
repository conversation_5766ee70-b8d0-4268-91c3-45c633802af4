{"__meta": {"id": "Xdf71432ac5653f9a30547b7ca9765808", "datetime": "2024-11-28 11:29:30", "utime": 1732804170.891627, "method": "GET", "uri": "/api/profile/wallet/update-bonus-if-needed", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804163.523671, "end": 1732804170.891655, "duration": 7.367984056472778, "duration_str": "7.37s", "measures": [{"label": "Booting", "start": 1732804163.523671, "relative_start": 0, "end": **********.516101, "relative_end": **********.516101, "duration": 1.9924299716949463, "duration_str": "1.99s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.516115, "relative_start": 1.9924440383911133, "end": 1732804170.891658, "relative_end": 3.0994415283203125e-06, "duration": 5.375543117523193, "duration_str": "5.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14293792, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/profile/wallet/update-bonus-if-needed", "middleware": "api, auth:api", "controller": "App\\Http\\Controllers\\Api\\Profile\\WalletController@updateBonusIfNeeded", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=190\" onclick=\"\">app/Http/Controllers/Api/Profile/WalletController.php:190-209</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01669, "accumulated_duration_str": "16.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `wallets` where `user_id` = ******** limit 1", "type": "query", "params": [], "bindings": [********], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 193}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.616549, "duration": 0.01613, "duration_str": "16.13ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:193", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=193", "ajax": false, "filename": "WalletController.php", "line": "193"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 96.645}, {"sql": "update `wallets` set `balance_bonus_rollover` = 0, `wallets`.`updated_at` = '2024-11-28 11:29:25' where `id` = 279", "type": "query", "params": [], "bindings": [0, "2024-11-28 11:29:25", 279], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 204}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.704539, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "WalletController.php:204", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=204", "ajax": false, "filename": "WalletController.php", "line": "204"}, "connection": "china15tema2", "explain": null, "start_percent": 96.645, "width_percent": 3.355}]}, "models": {"data": {"App\\Models\\Wallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/profile/wallet/update-bonus-if-needed", "status_code": "<pre class=sf-dump id=sf-dump-2052643536 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2052643536\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2086406699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2086406699\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1740588248 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1740588248\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-403174183 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"758 characters\">_fbp=fb.1.1732723263756.71770186546337922; XSRF-TOKEN=eyJpdiI6IjdDclNIQkNmVnN3YnFqMFMyQzBtQmc9PSIsInZhbHVlIjoiSmNEMzg4MkFuKzQzRlV3TmR1eU1iWDl6cUhYTTVuREw5ekRNcitOVWtZd3hIK1B1dktuT3BROE1nTjkxZERhTnB2N2VhUS8wK0hvaWEycDV0ejRqaXAvR052RXl1d0ZDTGlDMFJRVEl3T1BSTWtGOE51MThDN2RTNGVib1F5NWciLCJtYWMiOiJjZDFmMTA2NzY5OWM3ZjJlNGNhOGMyOGZlMWU3MGVhZTI2ZGZlYjBjNTVlZTA2MGQzMjIyMzIxNzBhYzUyZWIyIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6InQxYitmSzVHS0tXby9zSXlXNjlsQ1E9PSIsInZhbHVlIjoiRkF4aGd6N08zaFU4UUhPY1dLakphOUhZaGJ4YkI5STVNV3NhNUMxYnBZQjVnNUZwRXhtV2lnMDlLUi9oZ0Y5UUFzWFFhdXA0MXExRnRlY0c3WVRheHhQeFIyeEsvaTdXa0d6am1hdzV5TnlYQ2hrK0RhdVQrSnlIOFZScXFLWk8iLCJtYWMiOiJmMzI2MzJlNDA5NWY3M2RmZTBhZjVkMzQ1YTEwZmQyOWNlNjE0ZTFiMzI0ZThmZGVjNzBjMTlhMDU5NDllM2FiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjdDclNIQkNmVnN3YnFqMFMyQzBtQmc9PSIsInZhbHVlIjoiSmNEMzg4MkFuKzQzRlV3TmR1eU1iWDl6cUhYTTVuREw5ekRNcitOVWtZd3hIK1B1dktuT3BROE1nTjkxZERhTnB2N2VhUS8wK0hvaWEycDV0ejRqaXAvR052RXl1d0ZDTGlDMFJRVEl3T1BSTWtGOE51MThDN2RTNGVib1F5NWciLCJtYWMiOiJjZDFmMTA2NzY5OWM3ZjJlNGNhOGMyOGZlMWU3MGVhZTI2ZGZlYjBjNTVlZTA2MGQzMjIyMzIxNzBhYzUyZWIyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ryhn09EKvtnlPa25E5IgpPwzgD9rUjhFMPeBzANn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-403174183\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1090063603 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_fbp</span>\" => \"<span class=sf-dump-str title=\"36 characters\">fb.1.1732723263756.71770186546337922</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjdDclNIQkNmVnN3YnFqMFMyQzBtQmc9PSIsInZhbHVlIjoiSmNEMzg4MkFuKzQzRlV3TmR1eU1iWDl6cUhYTTVuREw5ekRNcitOVWtZd3hIK1B1dktuT3BROE1nTjkxZERhTnB2N2VhUS8wK0hvaWEycDV0ejRqaXAvR052RXl1d0ZDTGlDMFJRVEl3T1BSTWtGOE51MThDN2RTNGVib1F5NWciLCJtYWMiOiJjZDFmMTA2NzY5OWM3ZjJlNGNhOGMyOGZlMWU3MGVhZTI2ZGZlYjBjNTVlZTA2MGQzMjIyMzIxNzBhYzUyZWIyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InQxYitmSzVHS0tXby9zSXlXNjlsQ1E9PSIsInZhbHVlIjoiRkF4aGd6N08zaFU4UUhPY1dLakphOUhZaGJ4YkI5STVNV3NhNUMxYnBZQjVnNUZwRXhtV2lnMDlLUi9oZ0Y5UUFzWFFhdXA0MXExRnRlY0c3WVRheHhQeFIyeEsvaTdXa0d6am1hdzV5TnlYQ2hrK0RhdVQrSnlIOFZScXFLWk8iLCJtYWMiOiJmMzI2MzJlNDA5NWY3M2RmZTBhZjVkMzQ1YTEwZmQyOWNlNjE0ZTFiMzI0ZThmZGVjNzBjMTlhMDU5NDllM2FiIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090063603\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:29:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1976043670 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1976043670\", {\"maxDepth\":0})</script>\n"}}