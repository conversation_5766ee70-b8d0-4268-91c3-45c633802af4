{"__meta": {"id": "Xedebf22d128cf6a587cc2cbba37f5366", "datetime": "2024-11-28 11:38:00", "utime": **********.448414, "method": "POST", "uri": "/livewire/update", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804679.056781, "end": **********.448435, "duration": 1.3916540145874023, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1732804679.056781, "relative_start": 0, "end": 1732804679.431124, "relative_end": 1732804679.431124, "duration": 0.3743429183959961, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732804679.431141, "relative_start": 0.3743598461151123, "end": **********.448438, "relative_end": 2.86102294921875e-06, "duration": 1.0172970294952393, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14056040, "peak_usage_str": "13MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02357, "accumulated_duration_str": "23.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}], "start": **********.373557, "duration": 0.01351, "duration_str": "13.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 57.319}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/User.php", "line": 176}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 34}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 183}], "start": **********.4041579, "duration": 0.010060000000000001, "duration_str": "10.06ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "china15tema2", "explain": null, "start_percent": 57.319, "width_percent": 42.681}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"filament.livewire.notifications #6kplhs7MAhpGUup0L1f0": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2445\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"6kplhs7MAhpGUup0L1f0\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO", "_previous": "array:1 [\n  \"url\" => \"https://job.forradapg.com/ad-min-can-admin/custom-style-pro\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "DashboardAdmin_filters": "null", "filament": "[]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-1878629988 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1878629988\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1920909451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1920909451\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-944212314 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"779 characters\">{&quot;data&quot;:{&quot;isFilamentNotificationsComponent&quot;:true,&quot;notifications&quot;:[{&quot;9d9900ae-7174-4f2e-9b04-64a5ee25a3c7&quot;:[{&quot;id&quot;:&quot;9d9900ae-7174-4f2e-9b04-64a5ee25a3c7&quot;,&quot;actions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;body&quot;:&quot;Dados alterados com sucesso!&quot;,&quot;color&quot;:null,&quot;duration&quot;:6000,&quot;icon&quot;:&quot;heroicon-o-check-circle&quot;,&quot;iconColor&quot;:&quot;success&quot;,&quot;status&quot;:&quot;success&quot;,&quot;title&quot;:&quot;Dados alterados&quot;,&quot;view&quot;:&quot;filament-notifications::notification&quot;,&quot;viewData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;Filament\\\\Notifications\\\\Collection&quot;,&quot;s&quot;:&quot;wrbl&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;6kplhs7MAhpGUup0L1f0&quot;,&quot;name&quot;:&quot;filament.livewire.notifications&quot;,&quot;path&quot;:&quot;ad-min-can-admin\\/custom-style-pro&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;pt_BR&quot;},&quot;checksum&quot;:&quot;f29022a0cb07e7348e64864a428b8cd176d916b8ae538449d8733a40fc09ed95&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__dispatch</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">notificationClosed</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9d9900ae-7174-4f2e-9b04-64a5ee25a3c7</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944212314\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1794500917 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1453 characters\">_ga=GA1.1.1588727896.1727224672; _ga_ELXYT6N9JP=GS1.1.1727224672.1.1.1727224687.0.0.0; _fbp=fb.1.1727224779412.25516585183138564; _gcl_au=1.1.525778040.1727793989.463541494.1727794072.1727794684; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjIvZmJyQkNJR1lFZHY1VytoTFVYYnc9PSIsInZhbHVlIjoiR0hWWE9pT3phMG0ra0tXNEZ0TWNaUkQxUWY5ODMyNDZaQjAzYzM1a3FITWdybGw3QVozRXc3U1VZL3JOK0xwRDJQVTd1QVN6YmdZZWNIRDhDVXFtcDlhR0JlbWNtQUYydm1DSHhUYlFENmVqdWlOclRKVWlwLzU3a2d2WlpXTjdxaUlxbm1hK29TTk9aQVNraGxJN2x1aGwxYnNPbituMUhsb3BiQWhCV2JSTFZjQlVyOGx3NE55cm9tSnJXMzd6WlFIenRIOUxTRGEvR0wrMXpNaFZheVdZSWpkZ2ZRbFREZDh5bnZrL281bz0iLCJtYWMiOiJhNDI3ODAyM2ViYTU0MWZkMjZkZTIxMDNiZDZhOGQ1MDI1YjVlYWUxMWE1YzlmZDc2YmE4ZGQ1OGEzYjlhMWY3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im9sTWZOdFNEWW9PYWkybjkvYUFic2c9PSIsInZhbHVlIjoiTlpObzcwWXdGem1zSElNclk4d3ltaG80Z3l0UEI4ZSttMDh6dzdtcUJ0WHFJZXFwUUJGSThuS2M4dktGTkxQVGpSaGdWd013c29NZ2k4VFo2d2pOTWJjT08wTjJPTEZhYm5pTVZ2L1RqdWFobm9HcVRhUVh5Nit5czdVUjFyYS8iLCJtYWMiOiI5ZTBiODc2NzA4ZWI3NGI0M2YxMzNjYzA0NTA5NzhlNWVkMTQ0YWEzZTdhNWU2ZTllODI3OGEyODc2MzMyNDVkIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6IlhjbFRUanFjSnZMUlQrWmxRQnFwR2c9PSIsInZhbHVlIjoiK2RnclNKRURFa2VHcXUyMEplNmo5b3dnMkxkNmlVMDR4SHk5S0RwUWJad3dpd1dUSFRKKzA1MVQ0dElYZVVuNTZCZHFpNE1WV3BBelgwY1U4Qk1MRlNZWkhLSVVkdm9iYVRuZ3hzbDNWVnUwUHUxekpLU3Q2TldPbVFQSjBReWwiLCJtYWMiOiIzNmRmNmZmMDQ2ZDQ2NjEyMzM3MWE1NzJkY2RlN2I0ZWQ3NGEzNjVkZmNhM2M1N2NhMGE3ZjFmODNjNzcyNjk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">https://job.forradapg.com/ad-min-can-admin/custom-style-pro</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1101</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794500917\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1143827296 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_ELXYT6N9JP</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_fbp</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|k6ltzNsW4jF5VozMk8iT6IZZNvamyc7LmcHCDAU4zPjYzTFQWJoTdHwM2NjD|$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">o1TmPiJzyXN9mGIu9vfXbq7Y9cZbdaDWS7sUKgqp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143827296\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1289197491 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:38:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImtRa1Q2dVhLT3QwelFKS0Q1anQza1E9PSIsInZhbHVlIjoicU1iYWRKUXBKd1JNWVZQZzQ4ekFYQlRBL1Q2WGxWWUw5L2tSUjQxR25uMXMzRERCRnJkTENodkZJdlZpcWgvUGErOUN6em5HZnJwUTRXall1dHB6amJlQXp4QXc3bnBacUtKMXlRSDdhSjZ2eDF0dnZSbVJjQnA2djZYTWxkZ3AiLCJtYWMiOiJiZTVkMjQ5ZjMyYmZmZDY0ZTdkYTg2NGM2OGQ0ZTY2MTIzNjc4Y2QyMDc2YmYyN2RiZTU5MWFiMzBhZWE2ZTJkIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 16:38:00 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6IklSaTM3NEwvMlkrWW0wVSs0UlZVbXc9PSIsInZhbHVlIjoiS0c3NzBMME13aFVkMjhDNlFBdENQRmdLcXh3b2YrSDBMVEdSUUlQdllmTmhWc1FsNGFIK09tYW45ZC9DOVJlamRQRE5tQmxZQUxuUVc0cHE3VmQ2c0srQTVZaVhxbHNmK3BqZi90ZUdySnJxTkFmbUhLVzRnMzJTNDhxUy8xWTciLCJtYWMiOiJiMmIyMzM5N2E0MDNjNGI4MzkwYTE4YzQ4MDJlM2I4MzJjNjM2NWJmYTZlOGQyNGZhNjVkZDliNGE1YWQ4YjQ5IiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 16:38:00 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImtRa1Q2dVhLT3QwelFKS0Q1anQza1E9PSIsInZhbHVlIjoicU1iYWRKUXBKd1JNWVZQZzQ4ekFYQlRBL1Q2WGxWWUw5L2tSUjQxR25uMXMzRERCRnJkTENodkZJdlZpcWgvUGErOUN6em5HZnJwUTRXall1dHB6amJlQXp4QXc3bnBacUtKMXlRSDdhSjZ2eDF0dnZSbVJjQnA2djZYTWxkZ3AiLCJtYWMiOiJiZTVkMjQ5ZjMyYmZmZDY0ZTdkYTg2NGM2OGQ0ZTY2MTIzNjc4Y2QyMDc2YmYyN2RiZTU5MWFiMzBhZWE2ZTJkIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 16:38:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6IklSaTM3NEwvMlkrWW0wVSs0UlZVbXc9PSIsInZhbHVlIjoiS0c3NzBMME13aFVkMjhDNlFBdENQRmdLcXh3b2YrSDBMVEdSUUlQdllmTmhWc1FsNGFIK09tYW45ZC9DOVJlamRQRE5tQmxZQUxuUVc0cHE3VmQ2c0srQTVZaVhxbHNmK3BqZi90ZUdySnJxTkFmbUhLVzRnMzJTNDhxUy8xWTciLCJtYWMiOiJiMmIyMzM5N2E0MDNjNGI4MzkwYTE4YzQ4MDJlM2I4MzJjNjM2NWJmYTZlOGQyNGZhNjVkZDliNGE1YWQ4YjQ5IiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 16:38:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1289197491\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1856770909 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://job.forradapg.com/ad-min-can-admin/custom-style-pro</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>DashboardAdmin_filters</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1856770909\", {\"maxDepth\":0})</script>\n"}}