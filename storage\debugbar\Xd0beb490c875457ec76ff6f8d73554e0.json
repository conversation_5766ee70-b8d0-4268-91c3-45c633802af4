{"__meta": {"id": "Xd0beb490c875457ec76ff6f8d73554e0", "datetime": "2024-11-28 13:57:49", "utime": **********.74206, "method": "POST", "uri": "/livewire/update", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732813066.002387, "end": **********.742082, "duration": 3.7396950721740723, "duration_str": "3.74s", "measures": [{"label": "Booting", "start": 1732813066.002387, "relative_start": 0, "end": 1732813067.281027, "relative_end": 1732813067.281027, "duration": 1.2786400318145752, "duration_str": "1.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732813067.281041, "relative_start": 1.278653860092163, "end": **********.742086, "relative_end": 3.814697265625e-06, "duration": 2.461045026779175, "duration_str": "2.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14058040, "peak_usage_str": "13MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 8, "templates": [{"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.560272, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::eeb545c356a817f56ecf5e68001e6e4d", "param_count": null, "params": [], "start": **********.584231, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/eeb545c356a817f56ecf5e68001e6e4d.blade.php__components::eeb545c356a817f56ecf5e68001e6e4d", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Feeb545c356a817f56ecf5e68001e6e4d.blade.php&line=1", "ajax": false, "filename": "eeb545c356a817f56ecf5e68001e6e4d.blade.php", "line": "?"}}, {"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.601299, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::bb2a00dce2af7923758c4a15eaed50fb", "param_count": null, "params": [], "start": **********.616446, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/bb2a00dce2af7923758c4a15eaed50fb.blade.php__components::bb2a00dce2af7923758c4a15eaed50fb", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Fbb2a00dce2af7923758c4a15eaed50fb.blade.php&line=1", "ajax": false, "filename": "bb2a00dce2af7923758c4a15eaed50fb.blade.php", "line": "?"}}, {"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.635316, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::24df2039ca2553dede44c819ca2b3052", "param_count": null, "params": [], "start": **********.674007, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/24df2039ca2553dede44c819ca2b3052.blade.php__components::24df2039ca2553dede44c819ca2b3052", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F24df2039ca2553dede44c819ca2b3052.blade.php&line=1", "ajax": false, "filename": "24df2039ca2553dede44c819ca2b3052.blade.php", "line": "?"}}, {"name": "__components::24df2039ca2553dede44c819ca2b3052", "param_count": null, "params": [], "start": **********.68385, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/24df2039ca2553dede44c819ca2b3052.blade.php__components::24df2039ca2553dede44c819ca2b3052", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F24df2039ca2553dede44c819ca2b3052.blade.php&line=1", "ajax": false, "filename": "24df2039ca2553dede44c819ca2b3052.blade.php", "line": "?"}}, {"name": "__components::24df2039ca2553dede44c819ca2b3052", "param_count": null, "params": [], "start": **********.716104, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/24df2039ca2553dede44c819ca2b3052.blade.php__components::24df2039ca2553dede44c819ca2b3052", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F24df2039ca2553dede44c819ca2b3052.blade.php&line=1", "ajax": false, "filename": "24df2039ca2553dede44c819ca2b3052.blade.php", "line": "?"}}]}, "route": {"uri": "POST livewire/update", "controller": "App\\Filament\\Admin\\Resources\\BannerResource\\Pages\\EditBanner@_finishUpload", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportFileUploads%2FWithFileUploads.php&line=28\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportFileUploads/WithFileUploads.php:28-51</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.027500000000000004, "accumulated_duration_str": "27.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}], "start": **********.251468, "duration": 0.011460000000000001, "duration_str": "11.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 41.673}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/User.php", "line": 176}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 34}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 183}], "start": **********.292872, "duration": 0.010490000000000001, "duration_str": "10.49ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "china15tema2", "explain": null, "start_percent": 41.673, "width_percent": 38.145}, {"sql": "select * from `banners` where `banners`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 92}], "start": **********.336131, "duration": 0.00555, "duration_str": "5.55ms", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "china15tema2", "explain": null, "start_percent": 79.818, "width_percent": 20.182}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Banner": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FBanner.php&line=1", "ajax": false, "filename": "Banner.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": {"app.filament.admin.resources.banner-resource.pages.edit-banner #kwlc14rRVT07RntH5NOY": "array:4 [\n  \"data\" => array:19 [\n    \"data\" => array:9 [\n      \"id\" => 15\n      \"link\" => \"/\"\n      \"image\" => array:1 [\n        \"ebe596ed-4a61-4fe2-8752-ddd91f0752d4\" => Livewire\\Features\\SupportFileUploads\\TemporaryUploadedFile {#3604\n          -test: false\n          -originalName: \"gPM1RsAjphh8tumpFYf6g4D7xhY1X3-metadz0xMjAwICgxKS53ZWJw-.webp\"\n          -mimeType: \"application/octet-stream\"\n          -error: 0\n          #hashName: null\n          #disk: \"local\"\n          #storage: Illuminate\\Filesystem\\FilesystemAdapter {#3022\n            #driver: League\\Flysystem\\Filesystem {#2684\n              -config: League\\Flysystem\\Config {#3583\n                -options: []\n              }\n              -pathNormalizer: League\\Flysystem\\WhitespacePathNormalizer {#2892}\n              -adapter: League\\Flysystem\\Local\\LocalFilesystemAdapter {#3038\n                -prefixer: League\\Flysystem\\PathPrefixer {#2491\n                  -prefix: \"/home/<USER>/htdocs/job.forradapg.com/storage/app/\"\n                  -separator: \"/\"\n                }\n                -visibility: League\\Flysystem\\UnixVisibility\\PortableVisibilityConverter {#2708\n                  -filePublic: 420\n                  -filePrivate: 384\n                  -directoryPublic: 493\n                  -directoryPrivate: 448\n                  -defaultForDirectories: \"private\"\n                }\n                -mimeTypeDetector: League\\Flysystem\\Local\\FallbackMimeTypeDetector {#2592\n                  -detector: League\\MimeTypeDetection\\FinfoMimeTypeDetector {#2426\n                    -finfo: finfo {#2960}\n                    -extensionMap: League\\MimeTypeDetection\\GeneratedExtensionToMimeTypeMap {#2906}\n                    -bufferSampleSize: null\n                    -inconclusiveMimetypes: array:5 [\n                      0 => \"application/x-empty\"\n                      1 => \"text/plain\"\n                      2 => \"text/x-asm\"\n                      3 => \"application/octet-stream\"\n                      4 => \"inode/x-empty\"\n                    ]\n                  }\n                  -inconclusiveMimetypes: array:5 [\n                    0 => \"application/x-empty\"\n                    1 => \"text/plain\"\n                    2 => \"text/x-asm\"\n                    3 => \"application/octet-stream\"\n                    4 => \"inode/x-empty\"\n                  ]\n                  -useInconclusiveMimeTypeFallback: false\n                }\n                -rootLocation: \"/home/<USER>/htdocs/job.forradapg.com/storage/app\"\n                -rootLocationIsSetup: true\n                -writeFlags: 2\n                -linkHandling: 2\n              }\n              -publicUrlGenerator: null\n              -temporaryUrlGenerator: null\n            }\n            #adapter: League\\Flysystem\\Local\\LocalFilesystemAdapter {#3038}\n            #config: array:3 [\n              \"driver\" => \"local\"\n              \"root\" => \"/home/<USER>/htdocs/job.forradapg.com/storage/app\"\n              \"throw\" => false\n            ]\n            #prefixer: League\\Flysystem\\PathPrefixer {#3483\n              -prefix: \"/home/<USER>/htdocs/job.forradapg.com/storage/app/\"\n              -separator: \"/\"\n            }\n            #temporaryUrlCallback: null\n          }\n          #path: \"livewire-tmp/gPM1RsAjphh8tumpFYf6g4D7xhY1X3-metadz0xMjAwICgxKS53ZWJw-.webp\"\n          path: \"/home/<USER>/htdocs/job.forradapg.com/storage/app/livewire-tmp\"\n          filename: \"gPM1RsAjphh8tumpFYf6g4D7xhY1X3-metadz0xMjAwICgxKS53ZWJw-.webp\"\n          basename: \"phpicqmbP\"\n          pathname: \"/home/<USER>/htdocs/job.forradapg.com/storage/app/livewire-tmp/gPM1RsAjphh8tumpFYf6g4D7xhY1X3-metadz0xMjAwICgxKS53ZWJw-.webp\"\n          extension: \"\"\n          realPath: \"/home/<USER>/htdocs/job.forradapg.com/storage/app/livewire-tmp/gPM1RsAjphh8tumpFYf6g4D7xhY1X3-metadz0xMjAwICgxKS53ZWJw-.webp\"\n          aTime: 2024-11-28 13:57:49\n          mTime: 2024-11-28 13:57:49\n          cTime: 2024-11-28 13:57:49\n          inode: 11563\n          size: 195546\n          writable: false\n          readable: false\n          executable: false\n          file: false\n          dir: false\n          link: false\n        }\n      ]\n      \"type\" => \"carousel\"\n      \"description\" => \"...\"\n      \"created_at\" => \"2024-04-06T01:58:25.000000Z\"\n      \"updated_at\" => \"2024-10-21T01:40:24.000000Z\"\n      \"mobile_image\" => \"\"\n      \"approval_password_save\" => null\n    ]\n    \"previousUrl\" => \"https://job.forradapg.com/ad-min-can-admin/banners\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Banner {#2788\n      #connection: \"mysql\"\n      #table: \"banners\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:8 [\n        \"id\" => 15\n        \"link\" => \"/\"\n        \"image\" => \"01JAPP0MZRVGTYXDCTD9PGPYTW.avif\"\n        \"type\" => \"carousel\"\n        \"description\" => \"...\"\n        \"created_at\" => \"2024-04-05 22:58:25\"\n        \"updated_at\" => \"2024-10-20 22:40:24\"\n        \"mobile_image\" => \"\"\n      ]\n      #original: array:8 [\n        \"id\" => 15\n        \"link\" => \"/\"\n        \"image\" => \"01JAPP0MZRVGTYXDCTD9PGPYTW.avif\"\n        \"type\" => \"carousel\"\n        \"description\" => \"...\"\n        \"created_at\" => \"2024-04-05 22:58:25\"\n        \"updated_at\" => \"2024-10-20 22:40:24\"\n        \"mobile_image\" => \"\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:4 [\n        0 => \"image\"\n        1 => \"type\"\n        2 => \"description\"\n        3 => \"link\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.admin.resources.banner-resource.pages.edit-banner\"\n  \"component\" => \"App\\Filament\\Admin\\Resources\\BannerResource\\Pages\\EditBanner\"\n  \"id\" => \"kwlc14rRVT07RntH5NOY\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FolWRu7ETNojsciEWiwvxgelKrYSvdZu8ZgtdZqn", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO", "_previous": "array:1 [\n  \"url\" => \"https://job.forradapg.com/ad-min-can-admin/banners/15/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-617395956 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-617395956\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1595647564 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1595647564\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-399430213 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FolWRu7ETNojsciEWiwvxgelKrYSvdZu8ZgtdZqn</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1339 characters\">{&quot;data&quot;:{&quot;data&quot;:[{&quot;id&quot;:15,&quot;link&quot;:&quot;\\/&quot;,&quot;image&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;type&quot;:&quot;carousel&quot;,&quot;description&quot;:&quot;...&quot;,&quot;created_at&quot;:&quot;2024-04-06T01:58:25.000000Z&quot;,&quot;updated_at&quot;:&quot;2024-10-21T01:40:24.000000Z&quot;,&quot;mobile_image&quot;:&quot;&quot;,&quot;approval_password_save&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;previousUrl&quot;:&quot;https:\\/\\/job.forradapg.com\\/ad-min-can-admin\\/banners&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;activeRelationManager&quot;:null,&quot;record&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Banner&quot;,&quot;key&quot;:15,&quot;s&quot;:&quot;mdl&quot;}],&quot;savedDataHash&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;kwlc14rRVT07RntH5NOY&quot;,&quot;name&quot;:&quot;app.filament.admin.resources.banner-resource.pages.edit-banner&quot;,&quot;path&quot;:&quot;ad-min-can-admin\\/banners\\/15\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;pt_BR&quot;},&quot;checksum&quot;:&quot;d2c9072adb976d971b7d57845f428d8dfa5217760a58836da112d25a51b7028b&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">_finishUpload</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">data.image.ebe596ed-4a61-4fe2-8752-ddd91f0752d4</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">/gPM1RsAjphh8tumpFYf6g4D7xhY1X3-metadz0xMjAwICgxKS53ZWJw-.webp</span>\"\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-const>false</span>\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399430213\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-495831626 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1453 characters\">_ga=GA1.1.1588727896.1727224672; _ga_ELXYT6N9JP=GS1.1.1727224672.1.1.1727224687.0.0.0; _fbp=fb.1.1727224779412.25516585183138564; _gcl_au=1.1.525778040.1727793989.463541494.1727794072.1727794684; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjIvZmJyQkNJR1lFZHY1VytoTFVYYnc9PSIsInZhbHVlIjoiR0hWWE9pT3phMG0ra0tXNEZ0TWNaUkQxUWY5ODMyNDZaQjAzYzM1a3FITWdybGw3QVozRXc3U1VZL3JOK0xwRDJQVTd1QVN6YmdZZWNIRDhDVXFtcDlhR0JlbWNtQUYydm1DSHhUYlFENmVqdWlOclRKVWlwLzU3a2d2WlpXTjdxaUlxbm1hK29TTk9aQVNraGxJN2x1aGwxYnNPbituMUhsb3BiQWhCV2JSTFZjQlVyOGx3NE55cm9tSnJXMzd6WlFIenRIOUxTRGEvR0wrMXpNaFZheVdZSWpkZ2ZRbFREZDh5bnZrL281bz0iLCJtYWMiOiJhNDI3ODAyM2ViYTU0MWZkMjZkZTIxMDNiZDZhOGQ1MDI1YjVlYWUxMWE1YzlmZDc2YmE4ZGQ1OGEzYjlhMWY3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlMwMU5qaTRkUUxaY0d3b3YyRW12MUE9PSIsInZhbHVlIjoiV1ppR3FUWGx6QWQ5TVRVZUFrdnV0b01CS0lKQ3JmSkJIclZhNGxOZ3RBcmIvdkd5M0cyVlRhY2Y4UEYvK0RwMW9mWHlwYU5wUmFTcnI4a1pVQU4ycTRjaXJrODlIREs2b1Z1S0NwVnd1bDIyT0lBdEdJWUxtKzFUVFBRK0NrSUsiLCJtYWMiOiI2YTlkNmZkZWZmOWVmZGEwNzU4YWUyYWIwYTZlZTU0MDg5NzExMGZlOGQ0MjViMGVkYzNkY2VlYzNiOGJkZjE3IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6IjhzcWRNZTVPaUhYWFI3d2RLYUlxR2c9PSIsInZhbHVlIjoiWkgvbW1YSEt0VGFKd3hBMWZLck96MTQ5TFlEbnYrTGhYV0pwR1ZscHlFQS9rUzBBOHVNNGhYbHVUMzEvQkVkSUM3cGhYeTlVUUR1ZnZLQ2RIZjgvZ1J4YVAwTGdXZ3Vld05VQzNPRytLTjE1a0RuMmgxWEQvMnFGdnVWWHZNbG8iLCJtYWMiOiJlZjcxNjc4YmMyMDE2MjQ5MmI0ODM5MjZlYWE1NTUyZmM2NjUxMmZiZWNhYmRhN2NiMTg2YjhlOGJjOTQxZWQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"58 characters\">https://job.forradapg.com/ad-min-can-admin/banners/15/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1793</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495831626\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2003773278 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_ELXYT6N9JP</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_fbp</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|k6ltzNsW4jF5VozMk8iT6IZZNvamyc7LmcHCDAU4zPjYzTFQWJoTdHwM2NjD|$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FolWRu7ETNojsciEWiwvxgelKrYSvdZu8ZgtdZqn</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DBzCPMApVq0pmypuI8QDLRsFwUspw4wKEldBwf9G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003773278\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1000594127 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 16:57:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IklZWGRnTmRkNXhWS1VOaUxoc1FlUGc9PSIsInZhbHVlIjoiRk1QM2Z0ZDZDZkJ5VFBLWWVyVVZzeDB3YWJyVEFiOE5KRDdrcmhodkNlYjhueHFKMmZjcFZVNFloUmhWR1hMRVBlTnBoRGlQdkRFUzdIcWovTnB0RGI3QmxkQ1BRb2hBMkhscFRFUFJlZk9kaHg4dmVKZ3M3dHJzWVNzVzlTbWUiLCJtYWMiOiJjNjA5OWExYzVhZGE4NTk3NmNjMGMzYzFmY2VlZjAwMWJlYWQwZTA2NTMwMjU2MWNkODc5MjIwNGIxYjhhMWZlIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 18:57:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6InJSVTl6RlFVNk1Gb1gzU21lT2tGYXc9PSIsInZhbHVlIjoiMEQ1eHV0ZHpybVh2QzVjMlBjb2RnejJESVp3Y1NmaWV4U01QN1UySWg0ZWRaY1g0c2RvcW1GTnUyVEpYa0pSQ3BKdEF5VnhvVzFGSWljT1dLMGpzVjdSYlFlZ2NqUFNpN1RQNjVsQUpqU0VabzJnVzZkenhIVHJCVEZSamRTSWMiLCJtYWMiOiIwODY3Y2M4ZTk3M2NkZTY3NTcyMDJhMWQyMWZjM2U1NmNlOWI2YmQwNGYyNDU1ZTdlZDMwODM5MWY4YzUwMDM2IiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 18:57:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IklZWGRnTmRkNXhWS1VOaUxoc1FlUGc9PSIsInZhbHVlIjoiRk1QM2Z0ZDZDZkJ5VFBLWWVyVVZzeDB3YWJyVEFiOE5KRDdrcmhodkNlYjhueHFKMmZjcFZVNFloUmhWR1hMRVBlTnBoRGlQdkRFUzdIcWovTnB0RGI3QmxkQ1BRb2hBMkhscFRFUFJlZk9kaHg4dmVKZ3M3dHJzWVNzVzlTbWUiLCJtYWMiOiJjNjA5OWExYzVhZGE4NTk3NmNjMGMzYzFmY2VlZjAwMWJlYWQwZTA2NTMwMjU2MWNkODc5MjIwNGIxYjhhMWZlIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 18:57:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6InJSVTl6RlFVNk1Gb1gzU21lT2tGYXc9PSIsInZhbHVlIjoiMEQ1eHV0ZHpybVh2QzVjMlBjb2RnejJESVp3Y1NmaWV4U01QN1UySWg0ZWRaY1g0c2RvcW1GTnUyVEpYa0pSQ3BKdEF5VnhvVzFGSWljT1dLMGpzVjdSYlFlZ2NqUFNpN1RQNjVsQUpqU0VabzJnVzZkenhIVHJCVEZSamRTSWMiLCJtYWMiOiIwODY3Y2M4ZTk3M2NkZTY3NTcyMDJhMWQyMWZjM2U1NmNlOWI2YmQwNGYyNDU1ZTdlZDMwODM5MWY4YzUwMDM2IiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 18:57:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000594127\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1071926603 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FolWRu7ETNojsciEWiwvxgelKrYSvdZu8ZgtdZqn</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"58 characters\">https://job.forradapg.com/ad-min-can-admin/banners/15/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1071926603\", {\"maxDepth\":0})</script>\n"}}