{"__meta": {"id": "Xc4dc5e37a8f410d4f7fbf1f56a112180", "datetime": "2024-11-28 11:31:40", "utime": 1732804300.603805, "method": "GET", "uri": "/api/games/all", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804292.740207, "end": 1732804300.603825, "duration": 7.8636181354522705, "duration_str": "7.86s", "measures": [{"label": "Booting", "start": 1732804292.740207, "relative_start": 0, "end": **********.438493, "relative_end": **********.438493, "duration": 1.6982860565185547, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.438502, "relative_start": 1.6982951164245605, "end": 1732804300.603828, "relative_end": 2.86102294921875e-06, "duration": 6.165325880050659, "duration_str": "6.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 15138880, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/games/all", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@index", "namespace": null, "prefix": "api/games", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=44\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:44-53</a>"}, "queries": {"nb_statements": 513, "nb_visible_statements": 500, "nb_excluded_statements": 13, "nb_failed_statements": 0, "accumulated_duration": 0.8589499999999994, "accumulated_duration_str": "859ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `providers` where exists (select * from `games` where `providers`.`id` = `games`.`provider_id` and `show_home` = 1 and `status` = 1) and `status` = 1 order by `name` desc", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.5171309, "duration": 0.01091, "duration_str": "10.91ms", "memory": 0, "memory_str": null, "filename": "GameController.php:50", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=50", "ajax": false, "filename": "GameController.php", "line": "50"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 1.27}, {"sql": "select * from `games` where `show_home` = 1 and `status` = 1 and `games`.`provider_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 11126) order by `views` desc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.565312, "duration": 0.0215, "duration_str": "21.5ms", "memory": 0, "memory_str": null, "filename": "GameController.php:50", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=50", "ajax": false, "filename": "GameController.php", "line": "50"}, "connection": "china15tema2", "explain": null, "start_percent": 1.27, "width_percent": 2.503}, {"sql": "select * from `providers` where `providers`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 11126)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.618922, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "GameController.php:50", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=50", "ajax": false, "filename": "GameController.php", "line": "50"}, "connection": "china15tema2", "explain": null, "start_percent": 3.773, "width_percent": 0.56}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 828 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [828], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.278516, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 4.333, "width_percent": 0.061}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 828 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [828], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.293557, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 4.394, "width_percent": 0.454}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 841 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [841], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.309614, "duration": 0.00635, "duration_str": "6.35ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 4.848, "width_percent": 0.739}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 841 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [841], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.32684, "duration": 0.00671, "duration_str": "6.71ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 5.587, "width_percent": 0.781}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 824 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [824], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.3428361, "duration": 0.0060999999999999995, "duration_str": "6.1ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 6.368, "width_percent": 0.71}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 824 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [824], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.353386, "duration": 0.0077, "duration_str": "7.7ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 7.078, "width_percent": 0.896}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 844 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [844], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.3786252, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 7.975, "width_percent": 0.057}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 844 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [844], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.393776, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 8.032, "width_percent": 0.475}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 851 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [851], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4142141, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 8.507, "width_percent": 0.421}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 851 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [851], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.430701, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 8.928, "width_percent": 0.054}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 830 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [830], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.442358, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 8.982, "width_percent": 0.383}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 830 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [830], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4578788, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 9.365, "width_percent": 0.233}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 831 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [831], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.474587, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 9.598, "width_percent": 0.048}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 831 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [831], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.484349, "duration": 0.009609999999999999, "duration_str": "9.61ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 9.645, "width_percent": 1.119}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1195 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.504467, "duration": 0.013470000000000001, "duration_str": "13.47ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 10.764, "width_percent": 1.568}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1195 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.527167, "duration": 0.00649, "duration_str": "6.49ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 12.332, "width_percent": 0.756}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 397 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [397], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.547065, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 13.088, "width_percent": 0.284}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 397 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [397], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.563394, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 13.372, "width_percent": 0.065}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1194 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1194], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.582227, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 13.437, "width_percent": 0.18}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1194 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1194], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.604096, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 13.618, "width_percent": 0.068}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1190 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1190], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.621237, "duration": 0.00462, "duration_str": "4.62ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 13.685, "width_percent": 0.538}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1190 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1190], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.639739, "duration": 0.009779999999999999, "duration_str": "9.78ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 14.223, "width_percent": 1.139}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1193 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1193], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.659821, "duration": 0.005030000000000001, "duration_str": "5.03ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 15.362, "width_percent": 0.586}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1193 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1193], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.684743, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 15.947, "width_percent": 0.048}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1192 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1192], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.695375, "duration": 0.0064, "duration_str": "6.4ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 15.995, "width_percent": 0.745}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1192 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1192], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.708183, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 16.74, "width_percent": 0.637}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1191 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1191], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.723144, "duration": 0.007, "duration_str": "7ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 17.377, "width_percent": 0.815}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1191 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1191], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.743115, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 18.192, "width_percent": 0.112}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1060 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1060], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.7611842, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 18.304, "width_percent": 0.051}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1060 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1060], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.77465, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 18.355, "width_percent": 0.364}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1066 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1066], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.787886, "duration": 0.005860000000000001, "duration_str": "5.86ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 18.719, "width_percent": 0.682}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1066 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1066], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8040278, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 19.402, "width_percent": 0.355}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1059 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1059], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.81862, "duration": 0.00728, "duration_str": "7.28ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 19.757, "width_percent": 0.848}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1059 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1059], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8386312, "duration": 0.00715, "duration_str": "7.15ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 20.604, "width_percent": 0.832}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1062 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1062], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8579202, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 21.437, "width_percent": 0.424}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1062 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1062], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8767579, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 21.86, "width_percent": 0.044}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1065 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1065], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.89085, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 21.905, "width_percent": 0.055}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1065 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1065], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.900354, "duration": 0.00653, "duration_str": "6.53ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 21.959, "width_percent": 0.76}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1075 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1075], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.919818, "duration": 0.00709, "duration_str": "7.09ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 22.72, "width_percent": 0.825}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1075 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1075], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.934987, "duration": 0.0067599999999999995, "duration_str": "6.76ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 23.545, "width_percent": 0.787}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1076 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1076], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.9516208, "duration": 0.01007, "duration_str": "10.07ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 24.332, "width_percent": 1.172}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1076 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1076], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.9664, "duration": 0.0074199999999999995, "duration_str": "7.42ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 25.504, "width_percent": 0.864}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 811 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [811], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.983335, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 26.368, "width_percent": 0.463}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 811 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [811], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.000251, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 26.832, "width_percent": 0.178}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 806 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [806], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.0191271, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 27.01, "width_percent": 0.055}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 806 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [806], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.023775, "duration": 0.01402, "duration_str": "14.02ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 27.064, "width_percent": 1.632}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 807 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [807], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.046647, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 28.697, "width_percent": 0.348}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 807 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [807], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.062166, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 29.045, "width_percent": 0.055}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 766 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [766], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.0763662, "duration": 0.00555, "duration_str": "5.55ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 29.099, "width_percent": 0.646}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 766 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [766], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.096266, "duration": 0.00959, "duration_str": "9.59ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 29.746, "width_percent": 1.116}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 799 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [799], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.1133952, "duration": 0.00957, "duration_str": "9.57ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 30.862, "width_percent": 1.114}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 799 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [799], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.14198, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 31.976, "width_percent": 0.058}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 805 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [805], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.156772, "duration": 0.005, "duration_str": "5ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 32.034, "width_percent": 0.582}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 805 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [805], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.174377, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 32.617, "width_percent": 0.387}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 810 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [810], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.1898222, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 33.003, "width_percent": 0.052}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 810 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [810], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.20258, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 33.055, "width_percent": 0.042}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 778 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [778], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.21606, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 33.097, "width_percent": 0.377}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 778 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [778], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.232974, "duration": 0.00894, "duration_str": "8.94ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 33.475, "width_percent": 1.041}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 763 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [763], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.257325, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 34.515, "width_percent": 0.506}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 763 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [763], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.278256, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 35.022, "width_percent": 0.179}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 764 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [764], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.299872, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 35.201, "width_percent": 0.063}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 764 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [764], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.318271, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 35.264, "width_percent": 0.192}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 765 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [765], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.340391, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 35.456, "width_percent": 0.071}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 765 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [765], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.354861, "duration": 0.00545, "duration_str": "5.45ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 35.527, "width_percent": 0.634}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 809 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [809], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.378266, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 36.162, "width_percent": 0.305}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 809 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [809], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.394718, "duration": 0.00638, "duration_str": "6.38ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 36.467, "width_percent": 0.743}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 781 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [781], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.415819, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 37.209, "width_percent": 0.638}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 781 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [781], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4393718, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 37.847, "width_percent": 0.07}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 240 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [240], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4631128, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 37.917, "width_percent": 0.068}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 240 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [240], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4814892, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 37.985, "width_percent": 0.075}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 226 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [226], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4951282, "duration": 0.0068200000000000005, "duration_str": "6.82ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 38.059, "width_percent": 0.794}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 226 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [226], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.519881, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 38.853, "width_percent": 0.065}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 65 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.541801, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 38.918, "width_percent": 0.057}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 65 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.551272, "duration": 0.01078, "duration_str": "10.78ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 38.975, "width_percent": 1.255}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 135 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [135], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.570268, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 40.231, "width_percent": 0.29}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 135 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [135], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.585399, "duration": 0.0077599999999999995, "duration_str": "7.76ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 40.52, "width_percent": 0.903}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 148 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [148], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.604391, "duration": 0.00577, "duration_str": "5.77ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 41.424, "width_percent": 0.672}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 148 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [148], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.624514, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 42.096, "width_percent": 0.076}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 170 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [170], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.643339, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 42.171, "width_percent": 0.059}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 170 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [170], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.656557, "duration": 0.0072, "duration_str": "7.2ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 42.231, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 171 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [171], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.677602, "duration": 0.00594, "duration_str": "5.94ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 43.069, "width_percent": 0.692}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 171 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [171], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.698607, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 43.76, "width_percent": 0.062}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 177 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [177], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.714364, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 43.822, "width_percent": 0.057}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 177 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [177], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.7258189, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 43.879, "width_percent": 0.472}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 179 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [179], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.747806, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 44.351, "width_percent": 0.061}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 179 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [179], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.759889, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 44.411, "width_percent": 0.272}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 185 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [185], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.7749372, "duration": 0.00524, "duration_str": "5.24ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 44.684, "width_percent": 0.61}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 185 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [185], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.792361, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 45.294, "width_percent": 0.263}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 198 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [198], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.80682, "duration": 0.00704, "duration_str": "7.04ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 45.557, "width_percent": 0.82}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 198 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [198], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.825532, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 46.376, "width_percent": 0.063}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 200 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [200], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.842896, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 46.439, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 200 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [200], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8574848, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 46.688, "width_percent": 0.05}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 202 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [202], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.872447, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 46.738, "width_percent": 0.143}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 202 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [202], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.895681, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 46.882, "width_percent": 0.061}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 204 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [204], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.914476, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 46.942, "width_percent": 0.058}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 204 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [204], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.924661, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 47, "width_percent": 0.128}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 208 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [208], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.942479, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 47.128, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.952749, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 47.378, "width_percent": 0.385}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9568088, "duration": 0.0048200000000000005, "duration_str": "4.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 47.763, "width_percent": 0.561}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9618099, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 48.324, "width_percent": 0.047}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.962702, "duration": 0.0028599999999999997, "duration_str": "2.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 48.371, "width_percent": 0.333}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.965702, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 48.704, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.966363, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 48.729, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.96671, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 48.754, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.967303, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 48.776, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.967613, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 48.798, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9682758, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 48.822, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.968695, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 48.852, "width_percent": 0.356}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.972404, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 49.208, "width_percent": 0.12}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9736001, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 49.328, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.974406, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 49.36, "width_percent": 0.197}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.976244, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 49.557, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977061, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 49.584, "width_percent": 0.056}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977692, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 49.64, "width_percent": 0.453}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.982111, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 50.093, "width_percent": 0.403}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9857678, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 50.495, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.986479, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 50.523, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.986815, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 50.548, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.987446, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 50.569, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.987841, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 50.6, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.988494, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 50.625, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.988873, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 50.651, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9895911, "duration": 0.0065899999999999995, "duration_str": "6.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 50.676, "width_percent": 0.767}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.99647, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 51.443, "width_percent": 0.041}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.997538, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 51.484, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.997989, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 51.516, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.998673, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 51.541, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.999047, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 51.566, "width_percent": 0.282}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.002188, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 51.848, "width_percent": 0.041}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.002733, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 51.889, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.003589, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 51.917, "width_percent": 0.243}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.005851, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 52.16, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.006773, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 52.185, "width_percent": 0.148}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0082018, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 52.332, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.009041, "duration": 0.00468, "duration_str": "4.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 52.359, "width_percent": 0.545}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0139341, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 52.904, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.014888, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 52.937, "width_percent": 0.183}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.016666, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 53.12, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0175712, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 53.149, "width_percent": 0.482}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.021914, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 53.631, "width_percent": 0.034}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0229561, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 53.664, "width_percent": 0.32}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.025949, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 53.985, "width_percent": 0.037}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.027078, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 54.022, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.027487, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 54.05, "width_percent": 0.037}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0284169, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 54.087, "width_percent": 0.356}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0316749, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 54.443, "width_percent": 0.228}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.034272, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 54.671, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.03467, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 54.698, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.035481, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 54.73, "width_percent": 0.135}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.03681, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 54.865, "width_percent": 0.088}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.038206, "duration": 0.00591, "duration_str": "5.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 54.953, "width_percent": 0.688}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.044418, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 55.641, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0453582, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 55.676, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.045785, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 55.709, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.049499, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 55.737, "width_percent": 0.038}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0500338, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 55.775, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.050981, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 55.803, "width_percent": 0.229}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.053097, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 56.032, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0538578, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 56.057, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.054399, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 56.087, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.055191, "duration": 0.00628, "duration_str": "6.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 56.116, "width_percent": 0.731}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.061681, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 56.847, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.062536, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 56.874, "width_percent": 0.353}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.065706, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.227, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.066411, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.249, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.066813, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.282, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0675938, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.305, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.067988, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.333, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.068732, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.357, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0690858, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.383, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0697498, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.408, "width_percent": 0.253}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.072047, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.661, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.072737, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.686, "width_percent": 0.098}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.073766, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 57.783, "width_percent": 0.361}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0808399, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 58.144, "width_percent": 0.048}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0814762, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 58.192, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.082426, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 58.225, "width_percent": 0.32}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0853531, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 58.545, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0861962, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 58.577, "width_percent": 0.094}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.087149, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 58.672, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.08798, "duration": 0.00488, "duration_str": "4.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 58.702, "width_percent": 0.568}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.09301, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 59.27, "width_percent": 0.227}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.095456, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 59.497, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.095852, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 59.525, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.0965762, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 59.551, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.097044, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 59.582, "width_percent": 0.405}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1010928, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 59.987, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1015122, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.015, "width_percent": 0.034}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1023421, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.049, "width_percent": 0.036}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.102798, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.085, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1035721, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.114, "width_percent": 0.444}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.107672, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.558, "width_percent": 0.034}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1087012, "duration": 0.00501, "duration_str": "5.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.591, "width_percent": 0.583}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.113911, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.175, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.114698, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.198, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1150599, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.225, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.115758, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.252, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.116089, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.275, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.117737, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.412, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.11808, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.435, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.118758, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.466, "width_percent": 0.328}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.121716, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.794, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.122338, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.817, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.122686, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.841, "width_percent": 0.112}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1241472, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.952, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1246161, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.987, "width_percent": 0.118}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1262872, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.105, "width_percent": 0.399}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1299472, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.504, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.131021, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.537, "width_percent": 0.148}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.132472, "duration": 0.00509, "duration_str": "5.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.685, "width_percent": 0.593}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.138273, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.277, "width_percent": 0.402}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.141962, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.679, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.142957, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.712, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1434531, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.746, "width_percent": 0.034}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.144388, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.78, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1447809, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.808, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1456358, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.837, "width_percent": 0.233}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.147815, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.07, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.14873, "duration": 0.004889999999999999, "duration_str": "4.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.1, "width_percent": 0.569}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.153797, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.67, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1544669, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.693, "width_percent": 0.377}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1578848, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.07, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1585982, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.098, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.159054, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.13, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1599228, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.164, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.160368, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.195, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.161319, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.224, "width_percent": 0.146}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.162732, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.369, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1635132, "duration": 0.00872, "duration_str": "8.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.393, "width_percent": 1.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.172461, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.408, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.17331, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.44, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.17375, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.475, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.174418, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.499, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.174766, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.524, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.175368, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.546, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.175704, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.57, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1763, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.592, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1766138, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.614, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.177234, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.641, "width_percent": 0.042}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.177738, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.683, "width_percent": 0.448}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1821332, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.131, "width_percent": 0.402}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1857579, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.532, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.186394, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.557, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.186722, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.579, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.18731, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.6, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.187697, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.626, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1884, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.651, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1887698, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.677, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.189642, "duration": 0.00809, "duration_str": "8.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.705, "width_percent": 0.942}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.1979358, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.647, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.19869, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.673, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.199033, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.699, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.199723, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.72, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.200071, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.746, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.200794, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.768, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2012029, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.798, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2018828, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.82, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.202225, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.846, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.203038, "duration": 0.005059999999999999, "duration_str": "5.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.868, "width_percent": 0.589}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2083142, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.457, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.209272, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.488, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.209676, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.519, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.210445, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.542, "width_percent": 0.085}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.21137, "duration": 0.00692, "duration_str": "6.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.627, "width_percent": 0.806}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.219142, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.433, "width_percent": 0.29}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.221842, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.722, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2227948, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.754, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.223222, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.784, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.224017, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.813, "width_percent": 0.492}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2284448, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.306, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2292478, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.332, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2296238, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.359, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.230419, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.389, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.230814, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.417, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.231757, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.444, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2321942, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.472, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2329261, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.504, "width_percent": 0.526}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.237654, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.03, "width_percent": 0.069}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2388942, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.098, "width_percent": 0.318}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.241798, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.416, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2425349, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.443, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.242907, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.471, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.243747, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.493, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.244116, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.519, "width_percent": 0.178}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.246187, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.697, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.246584, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.724, "width_percent": 0.205}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.248988, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.929, "width_percent": 0.541}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.253817, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.47, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.254581, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.493, "width_percent": 0.349}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2577689, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.842, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.258556, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.873, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.258939, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.902, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2597342, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.926, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.260154, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.959, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.260927, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.986, "width_percent": 0.183}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.262662, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.168, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2634609, "duration": 0.00515, "duration_str": "5.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.196, "width_percent": 0.6}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.268822, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.796, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.269829, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.825, "width_percent": 0.303}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.272627, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.128, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.273505, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.163, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.273878, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.191, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.274626, "duration": 0.00297, "duration_str": "2.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.213, "width_percent": 0.346}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.277759, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.559, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.278527, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.582, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.278888, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.609, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.279499, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.632, "width_percent": 0.453}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.283519, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.085, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.284132, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.108, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.284458, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.13, "width_percent": 0.488}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.289116, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.618, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.289486, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.645, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2901409, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.67, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.29051, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.698, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2911649, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.726, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.291508, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.751, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.292153, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.775, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.292496, "duration": 0.00507, "duration_str": "5.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.801, "width_percent": 0.59}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.298092, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.391, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.298471, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.42, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.2992501, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.455, "width_percent": 0.278}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.301816, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.733, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.302628, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.76, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.303043, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.791, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3039, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.818, "width_percent": 0.201}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.305809, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.02, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.306666, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.048, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.307083, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.077, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.307949, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.107, "width_percent": 0.196}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.309781, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.303, "width_percent": 0.461}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.314326, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.764, "width_percent": 0.383}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.317775, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.147, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.31852, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.173, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3189409, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.205, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.319578, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.229, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.319912, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.254, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.320604, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.276, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.321032, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.307, "width_percent": 0.064}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.322125, "duration": 0.00704, "duration_str": "7.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.371, "width_percent": 0.82}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.329466, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.191, "width_percent": 0.034}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.330594, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.225, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.331043, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.257, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3320122, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.285, "width_percent": 0.034}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.33248, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.319, "width_percent": 0.038}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3389409, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.357, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.339375, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.388, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.340108, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.412, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.340453, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.435, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.341092, "duration": 0.0045, "duration_str": "4.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.456, "width_percent": 0.524}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3457909, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.98, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.346494, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.002, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.346824, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.027, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3475049, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.056, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.347863, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.083, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3486102, "duration": 0.00523, "duration_str": "5.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.108, "width_percent": 0.609}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3541381, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.717, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3552341, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.749, "width_percent": 0.034}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3556879, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.782, "width_percent": 0.229}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3582442, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.012, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.358677, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.043, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.359467, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.069, "width_percent": 0.389}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3630059, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.458, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.363801, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.487, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.364164, "duration": 0.0052699999999999995, "duration_str": "5.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.511, "width_percent": 0.614}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.370069, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.125, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.370455, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.15, "width_percent": 0.044}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.37142, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.195, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3718479, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.224, "width_percent": 0.09}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.373205, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.313, "width_percent": 0.17}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.374825, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.483, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.375508, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.51, "width_percent": 0.241}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.377734, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.751, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.378565, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.779, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3789902, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.81, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.379868, "duration": 0.00566, "duration_str": "5.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.842, "width_percent": 0.659}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3873432, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.501, "width_percent": 0.036}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3884459, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.537, "width_percent": 0.109}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.389571, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.646, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.39047, "duration": 0.00314, "duration_str": "3.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.677, "width_percent": 0.366}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3937929, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.042, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.394682, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.072, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3950799, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.1, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.3959029, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.127, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.396307, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.155, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.397142, "duration": 0.00458, "duration_str": "4.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.181, "width_percent": 0.533}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.401962, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.714, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4029648, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.745, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4033902, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.776, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.404295, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.807, "width_percent": 0.63}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4099498, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.437, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.411043, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.467, "width_percent": 0.306}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.413861, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.773, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.41481, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.806, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.415232, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.836, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4161222, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.865, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4165368, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.897, "width_percent": 0.356}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.420295, "duration": 0.00515, "duration_str": "5.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.253, "width_percent": 0.6}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4256692, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.853, "width_percent": 0.034}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.426595, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.886, "width_percent": 0.362}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4298942, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.248, "width_percent": 0.033}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.430835, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.281, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.431171, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.305, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.432107, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.328, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4325478, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.363, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.437483, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.393, "width_percent": 0.04}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.438035, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.432, "width_percent": 0.036}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.439037, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.468, "width_percent": 0.095}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.440003, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.564, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.440846, "duration": 0.004860000000000001, "duration_str": "4.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.591, "width_percent": 0.566}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4459348, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.157, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4468749, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.187, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4473028, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.217, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.448166, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.244, "width_percent": 0.21}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4501078, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.453, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4509249, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.481, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4512599, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.506, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.452013, "duration": 0.005719999999999999, "duration_str": "5.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.533, "width_percent": 0.666}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.458075, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.198, "width_percent": 0.235}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.460958, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.434, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.461366, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.462, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.462208, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.491, "width_percent": 0.387}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.465764, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.877, "width_percent": 0.038}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.466831, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.916, "width_percent": 0.036}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.467302, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.952, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4682, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.983, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.468608, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.012, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.469398, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.039, "width_percent": 0.506}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.474, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.545, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.474837, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.572, "width_percent": 0.32}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.477739, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.892, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.47838, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.915, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.478782, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.945, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.47941, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.969, "width_percent": 0.092}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4803638, "duration": 0.00513, "duration_str": "5.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.061, "width_percent": 0.597}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.486028, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.658, "width_percent": 0.416}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.489771, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.074, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4905639, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.1, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.49091, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.125, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.491586, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.147, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.491948, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.175, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.492558, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.199, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.4929318, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.224, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.493584, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.251, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.493943, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.278, "width_percent": 0.02}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.494701, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.298, "width_percent": 0.336}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.497758, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.634, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.498466, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.655, "width_percent": 0.221}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5005078, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.876, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5012898, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.904, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.501664, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.931, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.502393, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.953, "width_percent": 0.484}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5067918, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.437, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5075998, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.466, "width_percent": 0.088}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.508501, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.555, "width_percent": 0.227}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5109808, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.782, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.511355, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.811, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5119982, "duration": 0.00571, "duration_str": "5.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.834, "width_percent": 0.665}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.517951, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.499, "width_percent": 0.201}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.520529, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.701, "width_percent": 0.036}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.521029, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.737, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5261788, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.768, "width_percent": 0.045}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.526759, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.813, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.527593, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.841, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.52802, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.872, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.528775, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.902, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5291562, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.928, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.530113, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.959, "width_percent": 0.286}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.532742, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.245, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.537817, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.27, "width_percent": 0.05}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.538417, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.32, "width_percent": 0.031}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.539373, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.351, "width_percent": 0.249}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.541718, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.601, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.542536, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.627, "width_percent": 0.373}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.545965, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.54687, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.029, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5472739, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.057, "width_percent": 0.275}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.550133, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.332, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.550498, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.357, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5512352, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.386, "width_percent": 0.278}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.553828, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.665, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.554825, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.7, "width_percent": 0.322}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.55775, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.022, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.558447, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.046, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.558803, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.072, "width_percent": 0.235}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5613248, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.307, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.561721, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.334, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.562427, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.356, "width_percent": 0.369}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.565778, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.725, "width_percent": 0.026}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.566558, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.751, "width_percent": 0.232}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.568699, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.982, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.569403, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.005, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.569748, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.029, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.570488, "duration": 0.00298, "duration_str": "2.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.05, "width_percent": 0.347}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.57366, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.397, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.574489, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.426, "width_percent": 0.361}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.5777519, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.787, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732804300.57839, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.81, "width_percent": 0.19}, {"sql": "... 13 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"App\\Models\\Game": {"value": 255, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=1", "ajax": false, "filename": "Game.php", "line": "?"}}, "App\\Models\\Provider": {"value": 36, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FProvider.php&line=1", "ajax": false, "filename": "Provider.php", "line": "?"}}}, "count": 291, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/games/all", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-75568397 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-75568397\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6Im9GVlhWUWhIR0luVnhEVEFlRzIzamc9PSIsInZhbHVlIjoiNnM4N0RBVnFCeFdPbFFTOXlWZW5aOXN1ODBxTnduYUF3WU9yQitaK2M1MERFT0pHc2NCREtvcE9VVCtVN2krNWh2STBGbEhraTNkdnI2TUxHR05McTVWb1I0QlpPcDJPWjhCeWpsZHNVcVNxRG1IS3crQ2VqSTFCY2kxWVNORUkiLCJtYWMiOiJmOWJmMjk5ZTBjMTA2MDc2ZTA1ZWRkMTNlYmM4NzJlNDA5ZDQxY2M5ODljMzQ3ZDYzZGZmYmNiZGU0YWQ2NDNhIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6InZwcEdpdmh0RDFXcFIrdmFkWEk3SWc9PSIsInZhbHVlIjoicTRCZTVwRGo1a1BlMFZyUWxmRHBXaGRFbTBudHB2NFZLdlBLR05uWFdEQmhqODhwUWdFK2V2OFFLTlRzWTY1WmFXVGp6bjA5MEcwMVo5TVBUYzQ5SzZoemNIT1krczNTc09WN0tWaVQyYjZiZkZqeis4SGNDTUxDVm54dmVCYTciLCJtYWMiOiI2M2NjMWIxOTEyMjhjOGM0NzNjNGM0MzZkNDU2ODlhMzFkYmRjOWRjOWYyZWY2NDE1ZGJlNTgxM2E1Y2I2NDM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im9GVlhWUWhIR0luVnhEVEFlRzIzamc9PSIsInZhbHVlIjoiNnM4N0RBVnFCeFdPbFFTOXlWZW5aOXN1ODBxTnduYUF3WU9yQitaK2M1MERFT0pHc2NCREtvcE9VVCtVN2krNWh2STBGbEhraTNkdnI2TUxHR05McTVWb1I0QlpPcDJPWjhCeWpsZHNVcVNxRG1IS3crQ2VqSTFCY2kxWVNORUkiLCJtYWMiOiJmOWJmMjk5ZTBjMTA2MDc2ZTA1ZWRkMTNlYmM4NzJlNDA5ZDQxY2M5ODljMzQ3ZDYzZGZmYmNiZGU0YWQ2NDNhIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZxogPn2IBCCd0wbASYCv5voRQyu5lNXDFz0pAZq2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-464526511 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im9GVlhWUWhIR0luVnhEVEFlRzIzamc9PSIsInZhbHVlIjoiNnM4N0RBVnFCeFdPbFFTOXlWZW5aOXN1ODBxTnduYUF3WU9yQitaK2M1MERFT0pHc2NCREtvcE9VVCtVN2krNWh2STBGbEhraTNkdnI2TUxHR05McTVWb1I0QlpPcDJPWjhCeWpsZHNVcVNxRG1IS3crQ2VqSTFCY2kxWVNORUkiLCJtYWMiOiJmOWJmMjk5ZTBjMTA2MDc2ZTA1ZWRkMTNlYmM4NzJlNDA5ZDQxY2M5ODljMzQ3ZDYzZGZmYmNiZGU0YWQ2NDNhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZwcEdpdmh0RDFXcFIrdmFkWEk3SWc9PSIsInZhbHVlIjoicTRCZTVwRGo1a1BlMFZyUWxmRHBXaGRFbTBudHB2NFZLdlBLR05uWFdEQmhqODhwUWdFK2V2OFFLTlRzWTY1WmFXVGp6bjA5MEcwMVo5TVBUYzQ5SzZoemNIT1krczNTc09WN0tWaVQyYjZiZkZqeis4SGNDTUxDVm54dmVCYTciLCJtYWMiOiI2M2NjMWIxOTEyMjhjOGM0NzNjNGM0MzZkNDU2ODlhMzFkYmRjOWRjOWYyZWY2NDE1ZGJlNTgxM2E1Y2I2NDM2IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-464526511\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-110654334 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:31:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-110654334\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-438783216 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-438783216\", {\"maxDepth\":0})</script>\n"}}