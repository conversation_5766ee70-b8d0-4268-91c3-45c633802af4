{"__meta": {"id": "Xf131f2ca570af0c37cbd870dbe4026cd", "datetime": "2024-11-28 11:28:07", "utime": **********.71815, "method": "GET", "uri": "/api/settings/banners", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804077.44725, "end": **********.718176, "duration": 10.270925998687744, "duration_str": "10.27s", "measures": [{"label": "Booting", "start": 1732804077.44725, "relative_start": 0, "end": 1732804080.218862, "relative_end": 1732804080.218862, "duration": 2.7716121673583984, "duration_str": "2.77s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732804080.218877, "relative_start": 2.771627187728882, "end": **********.71818, "relative_end": 4.0531158447265625e-06, "duration": 7.499302864074707, "duration_str": "7.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14315016, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/settings/banners", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Settings\\BannerController@index", "namespace": null, "prefix": "api/settings/banners", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FSettings%2FBannerController.php&line=14\" onclick=\"\">app/Http/Controllers/Api/Settings/BannerController.php:14-17</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0025499999999999997, "accumulated_duration_str": "2.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `banners`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Settings/BannerController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Settings/BannerController.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.5942612, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "BannerController.php:16", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Settings/BannerController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Settings/BannerController.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FSettings%2FBannerController.php&line=16", "ajax": false, "filename": "BannerController.php", "line": "16"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Banner": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FBanner.php&line=1", "ajax": false, "filename": "Banner.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/settings/banners", "status_code": "<pre class=sf-dump id=sf-dump-1379820786 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1379820786\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-85952558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-85952558\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-842925886 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-842925886\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-712786896 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"758 characters\">_fbp=fb.1.1732723263756.71770186546337922; XSRF-TOKEN=eyJpdiI6IkZqOGF5aC81cmpWZVNRcm9QZGwrdVE9PSIsInZhbHVlIjoieG5NMVBnQm5TeXZnRXpLbzJjTXo0OHlFRHBVOFFHVjYvUndsRWdBZGIxNkFqNjVab2I2NVZHZEN6YVpxSHY4RWR2MTRTQUhRU0JBYjNOa3NSUFp5MUFrbmI4d0R4ekptdmJ1bVI3VnlVSEx5MGVSNDlDT1A4dk1IWDhyQ1hYbksiLCJtYWMiOiJiYTI4N2EwNTc4OWFhOWVlZWFkZmRjNTQ5MWQ4N2M3ZDg3NjFkNDRhY2ExOGI0MTFiMmQyNTgxMGQzYjkzYzA4IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6ImdzaExtZFhmNmJrS0xFM0RLZjdiOUE9PSIsInZhbHVlIjoiSnJraXhUbEo3cGsrODhaSEttRGFZakt3Q0k1bFZRNFFmQmdhYmRNY2plSHovR21KVElESGRPa2dMb280SCtIKzZLVWVwaFVEOExYa1J1ZVVOaFFwUnVCSTdGN2NRVHFKem1sakNDTUhaSEdEaVZwOFM0OElKRXFaTm03RGdnWU4iLCJtYWMiOiJiOWE3Y2M2MzY5NTBmYmQ1YTc2YTNjOTkwY2RiMDk5ZjU5ZGEyMTRlM2U4MGZjNjA3M2M5ZjRlZTVmZGYzODBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"133 characters\">Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZqOGF5aC81cmpWZVNRcm9QZGwrdVE9PSIsInZhbHVlIjoieG5NMVBnQm5TeXZnRXpLbzJjTXo0OHlFRHBVOFFHVjYvUndsRWdBZGIxNkFqNjVab2I2NVZHZEN6YVpxSHY4RWR2MTRTQUhRU0JBYjNOa3NSUFp5MUFrbmI4d0R4ekptdmJ1bVI3VnlVSEx5MGVSNDlDT1A4dk1IWDhyQ1hYbksiLCJtYWMiOiJiYTI4N2EwNTc4OWFhOWVlZWFkZmRjNTQ5MWQ4N2M3ZDg3NjFkNDRhY2ExOGI0MTFiMmQyNTgxMGQzYjkzYzA4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ryhn09EKvtnlPa25E5IgpPwzgD9rUjhFMPeBzANn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712786896\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2104019482 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_fbp</span>\" => \"<span class=sf-dump-str title=\"36 characters\">fb.1.1732723263756.71770186546337922</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZqOGF5aC81cmpWZVNRcm9QZGwrdVE9PSIsInZhbHVlIjoieG5NMVBnQm5TeXZnRXpLbzJjTXo0OHlFRHBVOFFHVjYvUndsRWdBZGIxNkFqNjVab2I2NVZHZEN6YVpxSHY4RWR2MTRTQUhRU0JBYjNOa3NSUFp5MUFrbmI4d0R4ekptdmJ1bVI3VnlVSEx5MGVSNDlDT1A4dk1IWDhyQ1hYbksiLCJtYWMiOiJiYTI4N2EwNTc4OWFhOWVlZWFkZmRjNTQ5MWQ4N2M3ZDg3NjFkNDRhY2ExOGI0MTFiMmQyNTgxMGQzYjkzYzA4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImdzaExtZFhmNmJrS0xFM0RLZjdiOUE9PSIsInZhbHVlIjoiSnJraXhUbEo3cGsrODhaSEttRGFZakt3Q0k1bFZRNFFmQmdhYmRNY2plSHovR21KVElESGRPa2dMb280SCtIKzZLVWVwaFVEOExYa1J1ZVVOaFFwUnVCSTdGN2NRVHFKem1sakNDTUhaSEdEaVZwOFM0OElKRXFaTm03RGdnWU4iLCJtYWMiOiJiOWE3Y2M2MzY5NTBmYmQ1YTc2YTNjOTkwY2RiMDk5ZjU5ZGEyMTRlM2U4MGZjNjA3M2M5ZjRlZTVmZGYzODBhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2104019482\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-759493273 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:28:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759493273\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1574225033 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1574225033\", {\"maxDepth\":0})</script>\n"}}