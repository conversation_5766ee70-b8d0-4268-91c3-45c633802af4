{"__meta": {"id": "Xde933782730f9aa07c2c8e7940b1a222", "datetime": "2024-11-28 17:06:34", "utime": 1732824394.080792, "method": "POST", "uri": "/playfiver/webhook", "ip": "************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732824388.188018, "end": 1732824394.080815, "duration": 5.892796993255615, "duration_str": "5.89s", "measures": [{"label": "Booting", "start": 1732824388.188018, "relative_start": 0, "end": 1732824389.687088, "relative_end": 1732824389.687088, "duration": 1.4990699291229248, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732824389.687111, "relative_start": 1.4990928173065186, "end": 1732824394.080818, "relative_end": 2.86102294921875e-06, "duration": 4.393707036972046, "duration_str": "4.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14082712, "peak_usage_str": "13MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST playfiver/webhook", "middleware": "web", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@webhookPlayFiver", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=424\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:424-427</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fJKr5bHau96S2D02ylVxaNCPKS8ZvDs0c2QKTZK2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/playfiver/webhook", "status_code": "<pre class=sf-dump id=sf-dump-883344319 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-883344319\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-388829565 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-388829565\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-647167207 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">BALANCE</span>\"\n  \"<span class=sf-dump-key>user_code</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647167207\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1613007341 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">GuzzleHttp/7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613007341\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2135403146 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2135403146\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1985832148 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 20:06:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImVjQlU2ekgydmZTbDBoRXNxS3lkZEE9PSIsInZhbHVlIjoiZlZmcFJSaVZnOXhGNlUxd1NHc1V3TXdnK1BYbUtGLzFlOC90R0tpNklGc3liWnJicGpLQUI4T0hrUDJUREhScUdFbldWRURCMWtFSnhKM1ZQUzZYc2dtcUxZTitJMmZEUWx0RlU3WGFSbmc4NzFPN0tteC9WQU1RN21WS0NtUjYiLCJtYWMiOiI4ODdlZWU2NTk1NDA2OTkzYmVhNmZjMmVkY2M3NTE1MDdkZWU2ODU3YjkxMDg0ZjNlZjVjNDE0Nzk5ZmQzYTJmIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 22:06:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6IllwcGIvR08wKzNmNCtuTnFCNDNqVHc9PSIsInZhbHVlIjoiOUhrNExBNHFvY0JrTHJwR1ROK2paM3BjdXB6K05lQk9MbGZOM3M0eWY3dncvNld3WUY1VnR1cUt4NDQ4aU9oWWFpdlVmb1Z0dFZoRUJIQzlnR2xYZXpmcWR6bm14OTd2OHh0SGovL0NWVWVoTnVpQ0RjUHNubjVGa3ZXazh1Y3IiLCJtYWMiOiJjZDI5ODVlNDdlYmMxZjUzZWZlNTlmMTU5MzY2ZmIzM2I4NmRmMmNkMWRkYzg5ZjIyNGQ2MjcwZWExMDYxMjkyIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 22:06:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImVjQlU2ekgydmZTbDBoRXNxS3lkZEE9PSIsInZhbHVlIjoiZlZmcFJSaVZnOXhGNlUxd1NHc1V3TXdnK1BYbUtGLzFlOC90R0tpNklGc3liWnJicGpLQUI4T0hrUDJUREhScUdFbldWRURCMWtFSnhKM1ZQUzZYc2dtcUxZTitJMmZEUWx0RlU3WGFSbmc4NzFPN0tteC9WQU1RN21WS0NtUjYiLCJtYWMiOiI4ODdlZWU2NTk1NDA2OTkzYmVhNmZjMmVkY2M3NTE1MDdkZWU2ODU3YjkxMDg0ZjNlZjVjNDE0Nzk5ZmQzYTJmIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 22:06:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6IllwcGIvR08wKzNmNCtuTnFCNDNqVHc9PSIsInZhbHVlIjoiOUhrNExBNHFvY0JrTHJwR1ROK2paM3BjdXB6K05lQk9MbGZOM3M0eWY3dncvNld3WUY1VnR1cUt4NDQ4aU9oWWFpdlVmb1Z0dFZoRUJIQzlnR2xYZXpmcWR6bm14OTd2OHh0SGovL0NWVWVoTnVpQ0RjUHNubjVGa3ZXazh1Y3IiLCJtYWMiOiJjZDI5ODVlNDdlYmMxZjUzZWZlNTlmMTU5MzY2ZmIzM2I4NmRmMmNkMWRkYzg5ZjIyNGQ2MjcwZWExMDYxMjkyIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 22:06:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985832148\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1942432112 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fJKr5bHau96S2D02ylVxaNCPKS8ZvDs0c2QKTZK2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1942432112\", {\"maxDepth\":0})</script>\n"}}