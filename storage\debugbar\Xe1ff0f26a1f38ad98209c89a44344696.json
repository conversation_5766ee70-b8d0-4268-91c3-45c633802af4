{"__meta": {"id": "Xe1ff0f26a1f38ad98209c89a44344696", "datetime": "2024-11-28 11:00:55", "utime": 1732802455.163613, "method": "POST", "uri": "/livewire/update", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732802453.567109, "end": 1732802455.163636, "duration": 1.596526861190796, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1732802453.567109, "relative_start": 0, "end": 1732802453.933892, "relative_end": 1732802453.933892, "duration": 0.36678290367126465, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732802453.933915, "relative_start": 0.3668057918548584, "end": 1732802455.163639, "relative_end": 3.0994415283203125e-06, "duration": 1.2297241687774658, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14052424, "peak_usage_str": "13MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02159, "accumulated_duration_str": "21.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}], "start": **********.866107, "duration": 0.00875, "duration_str": "8.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 40.528}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/User.php", "line": 176}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 34}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 183}], "start": **********.888917, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "china15tema2", "explain": null, "start_percent": 40.528, "width_percent": 9.125}, {"sql": "select count(*) as aggregate from `deposits` where month(`created_at`) = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.914447, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:46", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=46", "ajax": false, "filename": "WalletOverview.php", "line": "46"}, "connection": "china15tema2", "explain": null, "start_percent": 49.653, "width_percent": 8.661}, {"sql": "select count(*) as aggregate from `deposits` where month(`created_at`) = '11' and `status` = 1", "type": "query", "params": [], "bindings": ["11", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.930748, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:47", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=47", "ajax": false, "filename": "WalletOverview.php", "line": "47"}, "connection": "china15tema2", "explain": null, "start_percent": 58.314, "width_percent": 2.501}, {"sql": "select count(*) as aggregate from `deposits` where month(`created_at`) = '11' and `status` = 0", "type": "query", "params": [], "bindings": ["11", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.94535, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:48", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=48", "ajax": false, "filename": "WalletOverview.php", "line": "48"}, "connection": "china15tema2", "explain": null, "start_percent": 60.815, "width_percent": 2.501}, {"sql": "select sum(`amount`) as aggregate from `deposits` where month(`created_at`) = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 50}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.960268, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:50", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=50", "ajax": false, "filename": "WalletOverview.php", "line": "50"}, "connection": "china15tema2", "explain": null, "start_percent": 63.316, "width_percent": 2.131}, {"sql": "select sum(`amount`) as aggregate from `deposits` where month(`created_at`) = '11' and `status` = 1", "type": "query", "params": [], "bindings": ["11", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.975039, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:51", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=51", "ajax": false, "filename": "WalletOverview.php", "line": "51"}, "connection": "china15tema2", "explain": null, "start_percent": 65.447, "width_percent": 2.316}, {"sql": "select sum(`amount`) as aggregate from `deposits` where month(`created_at`) = '11' and `status` = 0", "type": "query", "params": [], "bindings": ["11", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.992906, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:52", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=52", "ajax": false, "filename": "WalletOverview.php", "line": "52"}, "connection": "china15tema2", "explain": null, "start_percent": 67.763, "width_percent": 2.964}, {"sql": "select count(*) as aggregate from `withdrawals` where month(`created_at`) = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": 1732802455.007543, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:55", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=55", "ajax": false, "filename": "WalletOverview.php", "line": "55"}, "connection": "china15tema2", "explain": null, "start_percent": 70.727, "width_percent": 7.735}, {"sql": "select count(*) as aggregate from `withdrawals` where month(`created_at`) = '11' and `status` = 1", "type": "query", "params": [], "bindings": ["11", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": 1732802455.0217328, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:56", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=56", "ajax": false, "filename": "WalletOverview.php", "line": "56"}, "connection": "china15tema2", "explain": null, "start_percent": 78.462, "width_percent": 1.899}, {"sql": "select count(*) as aggregate from `withdrawals` where month(`created_at`) = '11' and `status` = 0", "type": "query", "params": [], "bindings": ["11", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 57}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": 1732802455.032712, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:57", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=57", "ajax": false, "filename": "WalletOverview.php", "line": "57"}, "connection": "china15tema2", "explain": null, "start_percent": 80.361, "width_percent": 2.131}, {"sql": "select sum(`amount`) as aggregate from `withdrawals` where month(`created_at`) = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": 1732802455.0449998, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:59", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=59", "ajax": false, "filename": "WalletOverview.php", "line": "59"}, "connection": "china15tema2", "explain": null, "start_percent": 82.492, "width_percent": 1.899}, {"sql": "select sum(`amount`) as aggregate from `withdrawals` where month(`created_at`) = '11' and `status` = 1", "type": "query", "params": [], "bindings": ["11", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": 1732802455.056419, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:60", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=60", "ajax": false, "filename": "WalletOverview.php", "line": "60"}, "connection": "china15tema2", "explain": null, "start_percent": 84.391, "width_percent": 1.714}, {"sql": "select sum(`amount`) as aggregate from `withdrawals` where month(`created_at`) = '11' and `status` = 0", "type": "query", "params": [], "bindings": ["11", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": 1732802455.0677402, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:61", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=61", "ajax": false, "filename": "WalletOverview.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 86.105, "width_percent": 2.038}, {"sql": "select sum(`commission_paid`) as aggregate from `affiliate_histories` where month(`created_at`) = '11' and `commission_type` = 'revshare'", "type": "query", "params": [], "bindings": ["11", "revshare"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": 1732802455.079887, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:68", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=68", "ajax": false, "filename": "WalletOverview.php", "line": "68"}, "connection": "china15tema2", "explain": null, "start_percent": 88.143, "width_percent": 3.983}, {"sql": "select sum(`commission_paid`) as aggregate from `affiliate_histories` where month(`created_at`) = '11' and `commission_type` = 'revshare' and `commission_type` = 'cpa'", "type": "query", "params": [], "bindings": ["11", "revshare", "cpa"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 69}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": 1732802455.0923998, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:69", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=69", "ajax": false, "filename": "WalletOverview.php", "line": "69"}, "connection": "china15tema2", "explain": null, "start_percent": 92.126, "width_percent": 1.853}, {"sql": "select sum(`value_mostrar`) as aggregate from `baus` where month(`created_at`) = '11' and `status` = 3", "type": "query", "params": [], "bindings": ["11", 3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 72}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/widgets/src/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": 1732802455.1043398, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "WalletOverview.php:72", "source": {"index": 16, "namespace": null, "name": "app/Livewire/WalletOverview.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Livewire/WalletOverview.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FLivewire%2FWalletOverview.php&line=72", "ajax": false, "filename": "WalletOverview.php", "line": "72"}, "connection": "china15tema2", "explain": null, "start_percent": 93.979, "width_percent": 6.021}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"wallet-overview #o1TI3cjGFbmVlejBDQaJ": "array:4 [\n  \"data\" => array:1 [\n    \"filters\" => null\n  ]\n  \"name\" => \"wallet-overview\"\n  \"component\" => \"App\\Livewire\\WalletOverview\"\n  \"id\" => \"o1TI3cjGFbmVlejBDQaJ\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO", "_previous": "array:1 [\n  \"url\" => \"https://job.forradapg.com/ad-min-can-admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "DashboardAdmin_filters": "null", "filament": "[]"}, "request": {"path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-533310640 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-533310640\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-271166655 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-271166655\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1137444000 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"294 characters\">{&quot;data&quot;:{&quot;filters&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;o1TI3cjGFbmVlejBDQaJ&quot;,&quot;name&quot;:&quot;wallet-overview&quot;,&quot;path&quot;:&quot;ad-min-can-admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;props&quot;:[&quot;filters&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;pt_BR&quot;},&quot;checksum&quot;:&quot;b73ab7bd7fdec996f37442898f1521c2ac29d0bd420f78b6552d2b18bd115006&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137444000\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-581618339 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1453 characters\">_ga=GA1.1.1588727896.1727224672; _ga_ELXYT6N9JP=GS1.1.1727224672.1.1.1727224687.0.0.0; _fbp=fb.1.1727224779412.25516585183138564; _gcl_au=1.1.525778040.1727793989.463541494.1727794072.1727794684; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjIvZmJyQkNJR1lFZHY1VytoTFVYYnc9PSIsInZhbHVlIjoiR0hWWE9pT3phMG0ra0tXNEZ0TWNaUkQxUWY5ODMyNDZaQjAzYzM1a3FITWdybGw3QVozRXc3U1VZL3JOK0xwRDJQVTd1QVN6YmdZZWNIRDhDVXFtcDlhR0JlbWNtQUYydm1DSHhUYlFENmVqdWlOclRKVWlwLzU3a2d2WlpXTjdxaUlxbm1hK29TTk9aQVNraGxJN2x1aGwxYnNPbituMUhsb3BiQWhCV2JSTFZjQlVyOGx3NE55cm9tSnJXMzd6WlFIenRIOUxTRGEvR0wrMXpNaFZheVdZSWpkZ2ZRbFREZDh5bnZrL281bz0iLCJtYWMiOiJhNDI3ODAyM2ViYTU0MWZkMjZkZTIxMDNiZDZhOGQ1MDI1YjVlYWUxMWE1YzlmZDc2YmE4ZGQ1OGEzYjlhMWY3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Inc1WFRxeGZoaWhHL2x0RE5sdmR0NXc9PSIsInZhbHVlIjoiWTZMK1VNdlpmSlQ3RUkwcy9QbWZRSHRLREZWVkhoQWRQT24vams3Y3dISmg5d2dZY0RyaHNwYXFLbTY0cjBQZFAvOWVQMWVlaGYxTVVmWitrL2NZZmtBdEdqY2xQbW8xTW5xdWUxeWlQdTFhY0dwWmN6eW15dlFXd3RSRXZ2YnEiLCJtYWMiOiIxODE1NmQyMmMzYmM3MDQyN2Y2YjU0ZmQyMjMzMjE1ZTlmNDNmYmJkZTk0NzI3ZDI3NDU4MTcyM2ZmYTcwNGQ4IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6ImQ3UE1KdktsTUpXU0I5TDlOUlRNOGc9PSIsInZhbHVlIjoiZWZKSFRFci9CSHp5SjlPSFdqMU0zeW1XNWZLS3A1ekNvWVI3WEQ1Z2U4Z1Rrc3BzUXBFZjNmUThCYzdZY2pqVENpYlRkWWU0K3JlZUVSTHN6cjlIOEJWaTN0d1NySFh2TklZTlhvSWkrYzZiNXJFVW11NGJxcGx1bVpFZ3hhZzQiLCJtYWMiOiJkM2NjYzc5MDAxYmE0N2EyMTA2NDI1MzQ5YWMxMGFhZmQ0NWUwNmZlZTM2YjNjYThmM2UzYTI1MDViYmQwZDExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://job.forradapg.com/ad-min-can-admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">444</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581618339\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2051850599 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_ELXYT6N9JP</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_fbp</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|k6ltzNsW4jF5VozMk8iT6IZZNvamyc7LmcHCDAU4zPjYzTFQWJoTdHwM2NjD|$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">o1TmPiJzyXN9mGIu9vfXbq7Y9cZbdaDWS7sUKgqp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051850599\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1872751436 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:00:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IllCQlJ6bUpaVGpraE9hNnVBWkU1Tmc9PSIsInZhbHVlIjoiTWtXaUxiRXNFZi9sVnVpUmVIMDlhRUc5bitoT09MNTBvUWxsOVN6K1V2bk84MDQyWmhqT09TaFRZamt0azRwVE16Ymd3bWR3azN1WUh6R1hOT2Y1cDRmbXFXODFydVpjbENqSTd0VjgyYy8yVGdzeFBXNmFwbUZWYXpSbEdyRXYiLCJtYWMiOiI5ZTE0NzdlOTRiMjg5MWM0NDhhNzcwMzFhNGM1NTZkZjdkNTkyNWM0ZWFhYjIxZDVmMzg1ZDEzMTc5MjQyNGFhIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 16:00:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6Ijk3VXllUkhYeGorVTY1cndVelpVREE9PSIsInZhbHVlIjoiNWVFUjk4dXprQkpJeEZZNUtyMFp3NU10ZHZZUnBqblRveDJrbUJJdXk4R295UHZvR1djWERVTDk5bllmWUF0OVZiOExUQUgwTkpkcFUzcnlhWGpoc3RwcTN0d3pHNHdNYmpOay8rYjZ1Tm9CRmk4OTE0N0lUWDR3RTRhUnRxSnIiLCJtYWMiOiI0NmI5Nzk4Y2ViMDVhZTNlOTk3ZTg5NjUwOTRmNGMwMDZlYTM2OGI0ZGQ1MmQ1Y2U3YmU4MmFlM2ZhMTkyM2M5IiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 16:00:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IllCQlJ6bUpaVGpraE9hNnVBWkU1Tmc9PSIsInZhbHVlIjoiTWtXaUxiRXNFZi9sVnVpUmVIMDlhRUc5bitoT09MNTBvUWxsOVN6K1V2bk84MDQyWmhqT09TaFRZamt0azRwVE16Ymd3bWR3azN1WUh6R1hOT2Y1cDRmbXFXODFydVpjbENqSTd0VjgyYy8yVGdzeFBXNmFwbUZWYXpSbEdyRXYiLCJtYWMiOiI5ZTE0NzdlOTRiMjg5MWM0NDhhNzcwMzFhNGM1NTZkZjdkNTkyNWM0ZWFhYjIxZDVmMzg1ZDEzMTc5MjQyNGFhIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 16:00:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6Ijk3VXllUkhYeGorVTY1cndVelpVREE9PSIsInZhbHVlIjoiNWVFUjk4dXprQkpJeEZZNUtyMFp3NU10ZHZZUnBqblRveDJrbUJJdXk4R295UHZvR1djWERVTDk5bllmWUF0OVZiOExUQUgwTkpkcFUzcnlhWGpoc3RwcTN0d3pHNHdNYmpOay8rYjZ1Tm9CRmk4OTE0N0lUWDR3RTRhUnRxSnIiLCJtYWMiOiI0NmI5Nzk4Y2ViMDVhZTNlOTk3ZTg5NjUwOTRmNGMwMDZlYTM2OGI0ZGQ1MmQ1Y2U3YmU4MmFlM2ZhMTkyM2M5IiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 16:00:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1872751436\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1582721299 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4bZnu7mcNazwasxEta8WY2OOVoRTSKR6LHhLeQep</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://job.forradapg.com/ad-min-can-admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>DashboardAdmin_filters</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582721299\", {\"maxDepth\":0})</script>\n"}}