{"__meta": {"id": "Xfc5c0950b6f87bdc3f26710b051e0b49", "datetime": "2024-11-28 11:40:51", "utime": 1732804851.075791, "method": "GET", "uri": "/api/categories", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804845.622743, "end": 1732804851.075816, "duration": 5.453073024749756, "duration_str": "5.45s", "measures": [{"label": "Booting", "start": 1732804845.622743, "relative_start": 0, "end": **********.535128, "relative_end": **********.535128, "duration": 1.9123852252960205, "duration_str": "1.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.535142, "relative_start": 1.9123990535736084, "end": 1732804851.07582, "relative_end": 4.0531158447265625e-06, "duration": 3.540678024291992, "duration_str": "3.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14304832, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/categories", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Categories\\CategoryController@index", "namespace": null, "prefix": "api/categories", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FCategories%2FCategoryController.php&line=14\" onclick=\"\">app/Http/Controllers/Api/Categories/CategoryController.php:14-18</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02807, "accumulated_duration_str": "28.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Categories/CategoryController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Categories/CategoryController.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.610083, "duration": 0.02807, "duration_str": "28.07ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:16", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Categories/CategoryController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Categories/CategoryController.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FCategories%2FCategoryController.php&line=16", "ajax": false, "filename": "CategoryController.php", "line": "16"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Category": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}}, "count": 8, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/categories", "status_code": "<pre class=sf-dump id=sf-dump-1728126516 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1728126516\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1096611517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1096611517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-159509524 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-159509524\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-325105001 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IjRLMHkwcEQ5MWIxNXl2d0FSbGtzTEE9PSIsInZhbHVlIjoieGFGVEhuTmRVMnZnNkFRTG9aT3ZHeWE4YXE3WGQ1ZFY0MUQ4eDl6eVZhMjFUTFpTZnlUVGhLUkV3RUVFeEtUbVA4SFJLUTVoZDJDcDNUdWMyT3FPbkdadFNKNWtEMFQrb0JQY0pTc2hPZGNIc05DOWFUYUp3THZLK2NrVXE2b0UiLCJtYWMiOiI3YTQ0NzJhYTFhZWFlZGQ4YWQ1YTgyMDAyNzg0NWI1OGQxNjJjOTk4YzU4YmJkM2ZkNmUyNGI1NzM4ZWJmMmU4IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6IndESWhDNk5KaXpVanRsK3lEaUZ5OVE9PSIsInZhbHVlIjoid1NPRzBhYkZpYTVpMk4vMWdvYXVoUEtBR0lDV3Qyb1ppWnFWSzA2Ylg4Vks5SThFZUpVemhmTitmb3NoR2RTL1lyc3dDNWt0R3FOU3dyMWw5RTRnb2ZGaXhUK2VhL2t5R0JNT2U0c0dWaVdhR0RreFBiZ3VWV3VEQVZJdEJqc0oiLCJtYWMiOiI4OWViZGEyMmU3ZDYyODNiMTUxMGZkOTQyOWU0ZjIwMmM0ZTdkYjUwN2UxMmY0ODVjNGU3MDI0MGQ0MGM5NjIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRLMHkwcEQ5MWIxNXl2d0FSbGtzTEE9PSIsInZhbHVlIjoieGFGVEhuTmRVMnZnNkFRTG9aT3ZHeWE4YXE3WGQ1ZFY0MUQ4eDl6eVZhMjFUTFpTZnlUVGhLUkV3RUVFeEtUbVA4SFJLUTVoZDJDcDNUdWMyT3FPbkdadFNKNWtEMFQrb0JQY0pTc2hPZGNIc05DOWFUYUp3THZLK2NrVXE2b0UiLCJtYWMiOiI3YTQ0NzJhYTFhZWFlZGQ4YWQ1YTgyMDAyNzg0NWI1OGQxNjJjOTk4YzU4YmJkM2ZkNmUyNGI1NzM4ZWJmMmU4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZE0YVLZQjR9JyraSrLfdfqjJhqLh1JxweQcNpzyV</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325105001\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-797324563 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRLMHkwcEQ5MWIxNXl2d0FSbGtzTEE9PSIsInZhbHVlIjoieGFGVEhuTmRVMnZnNkFRTG9aT3ZHeWE4YXE3WGQ1ZFY0MUQ4eDl6eVZhMjFUTFpTZnlUVGhLUkV3RUVFeEtUbVA4SFJLUTVoZDJDcDNUdWMyT3FPbkdadFNKNWtEMFQrb0JQY0pTc2hPZGNIc05DOWFUYUp3THZLK2NrVXE2b0UiLCJtYWMiOiI3YTQ0NzJhYTFhZWFlZGQ4YWQ1YTgyMDAyNzg0NWI1OGQxNjJjOTk4YzU4YmJkM2ZkNmUyNGI1NzM4ZWJmMmU4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IndESWhDNk5KaXpVanRsK3lEaUZ5OVE9PSIsInZhbHVlIjoid1NPRzBhYkZpYTVpMk4vMWdvYXVoUEtBR0lDV3Qyb1ppWnFWSzA2Ylg4Vks5SThFZUpVemhmTitmb3NoR2RTL1lyc3dDNWt0R3FOU3dyMWw5RTRnb2ZGaXhUK2VhL2t5R0JNT2U0c0dWaVdhR0RreFBiZ3VWV3VEQVZJdEJqc0oiLCJtYWMiOiI4OWViZGEyMmU3ZDYyODNiMTUxMGZkOTQyOWU0ZjIwMmM0ZTdkYjUwN2UxMmY0ODVjNGU3MDI0MGQ0MGM5NjIyIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797324563\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-676694971 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:40:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676694971\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-86073481 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-86073481\", {\"maxDepth\":0})</script>\n"}}