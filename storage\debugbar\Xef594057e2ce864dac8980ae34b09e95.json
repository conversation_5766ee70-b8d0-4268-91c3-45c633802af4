{"__meta": {"id": "Xef594057e2ce864dac8980ae34b09e95", "datetime": "2024-11-28 16:15:28", "utime": **********.665597, "method": "GET", "uri": "/ad-min-can-admin/minha-cart<PERSON>/279/edit", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732821326.783942, "end": **********.665619, "duration": 1.8816769123077393, "duration_str": "1.88s", "measures": [{"label": "Booting", "start": 1732821326.783942, "relative_start": 0, "end": 1732821327.13977, "relative_end": 1732821327.13977, "duration": 0.35582804679870605, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732821327.139785, "relative_start": 0.35584306716918945, "end": **********.665622, "relative_end": 3.0994415283203125e-06, "duration": 1.5258369445800781, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14165104, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 12, "templates": [{"name": "__components::eeb545c356a817f56ecf5e68001e6e4d", "param_count": null, "params": [], "start": **********.295713, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/eeb545c356a817f56ecf5e68001e6e4d.blade.php__components::eeb545c356a817f56ecf5e68001e6e4d", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Feeb545c356a817f56ecf5e68001e6e4d.blade.php&line=1", "ajax": false, "filename": "eeb545c356a817f56ecf5e68001e6e4d.blade.php", "line": "?"}}, {"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.310811, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.316708, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.322196, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.327832, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.333335, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::f48d4e11b9e3a24dd3a2cb9546765aee", "param_count": null, "params": [], "start": **********.341922, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/f48d4e11b9e3a24dd3a2cb9546765aee.blade.php__components::f48d4e11b9e3a24dd3a2cb9546765aee", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2Ff48d4e11b9e3a24dd3a2cb9546765aee.blade.php&line=1", "ajax": false, "filename": "f48d4e11b9e3a24dd3a2cb9546765aee.blade.php", "line": "?"}}, {"name": "__components::24df2039ca2553dede44c819ca2b3052", "param_count": null, "params": [], "start": **********.355564, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/24df2039ca2553dede44c819ca2b3052.blade.php__components::24df2039ca2553dede44c819ca2b3052", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F24df2039ca2553dede44c819ca2b3052.blade.php&line=1", "ajax": false, "filename": "24df2039ca2553dede44c819ca2b3052.blade.php", "line": "?"}}, {"name": "__components::24df2039ca2553dede44c819ca2b3052", "param_count": null, "params": [], "start": **********.361192, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/24df2039ca2553dede44c819ca2b3052.blade.php__components::24df2039ca2553dede44c819ca2b3052", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F24df2039ca2553dede44c819ca2b3052.blade.php&line=1", "ajax": false, "filename": "24df2039ca2553dede44c819ca2b3052.blade.php", "line": "?"}}, {"name": "__components::24df2039ca2553dede44c819ca2b3052", "param_count": null, "params": [], "start": **********.373447, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/24df2039ca2553dede44c819ca2b3052.blade.php__components::24df2039ca2553dede44c819ca2b3052", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F24df2039ca2553dede44c819ca2b3052.blade.php&line=1", "ajax": false, "filename": "24df2039ca2553dede44c819ca2b3052.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.388222, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/storage/framework/views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament.components.logo", "param_count": null, "params": [], "start": **********.538819, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/resources/views/filament/components/logo.blade.phpfilament.components.logo", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fresources%2Fviews%2Ffilament%2Fcomponents%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}}]}, "route": {"uri": "GET ad-min-can-admin/minha-cart<PERSON>/{record}/edit", "domain": null, "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\CheckAdmin", "excluded_middleware": [], "controller": "App\\Filament\\Admin\\Resources\\WalletResource\\Pages\\EditWallet@__invoke", "as": "filament.admin.resources.minha-carteira.edit", "namespace": null, "prefix": "ad-min-can-admin/minha-carteira", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01069, "accumulated_duration_str": "10.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}], "start": **********.161395, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 26.286}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/User.php", "line": 176}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 34}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 55}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 183}], "start": **********.180987, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "china15tema2", "explain": null, "start_percent": 26.286, "width_percent": 8.419}, {"sql": "select * from `wallets` where `id` = '279' limit 1", "type": "query", "params": [], "bindings": ["279"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 192}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Pages/EditRecord.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.200968, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Resource.php:192", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 192}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource.php&line=192", "ajax": false, "filename": "Resource.php", "line": "192"}, "connection": "china15tema2", "explain": null, "start_percent": 34.705, "width_percent": 5.987}, {"sql": "select `name`, `id` from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Admin/Resources/WalletResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WalletResource.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/forms/src/Components/Select.php", "line": 652}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/forms/src/../resources/views/components/select.blade.php", "line": 168}], "start": **********.281922, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "WalletResource.php:90", "source": {"index": 14, "namespace": null, "name": "app/Filament/Admin/Resources/WalletResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WalletResource.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FWalletResource.php&line=90", "ajax": false, "filename": "WalletResource.php", "line": "90"}, "connection": "china15tema2", "explain": null, "start_percent": 40.692, "width_percent": 4.958}, {"sql": "select count(*) as aggregate from `deposits` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.392452, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "DepositResource.php:53", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FDepositResource.php&line=53", "ajax": false, "filename": "DepositResource.php", "line": "53"}, "connection": "china15tema2", "explain": null, "start_percent": 45.65, "width_percent": 20.206}, {"sql": "select count(*) as aggregate from `deposits` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.404694, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DepositResource.php:61", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FDepositResource.php&line=61", "ajax": false, "filename": "DepositResource.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 65.856, "width_percent": 2.9}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.41562, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "WithdrawalResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FWithdrawalResource.php&line=49", "ajax": false, "filename": "WithdrawalResource.php", "line": "49"}, "connection": "china15tema2", "explain": null, "start_percent": 68.756, "width_percent": 15.622}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.427589, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "WithdrawalResource.php:54", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FWithdrawalResource.php&line=54", "ajax": false, "filename": "WithdrawalResource.php", "line": "54"}, "connection": "china15tema2", "explain": null, "start_percent": 84.378, "width_percent": 3.087}, {"sql": "select count(*) as aggregate from `deposits` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.4930599, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DepositResource.php:53", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FDepositResource.php&line=53", "ajax": false, "filename": "DepositResource.php", "line": "53"}, "connection": "china15tema2", "explain": null, "start_percent": 87.465, "width_percent": 3.929}, {"sql": "select count(*) as aggregate from `deposits` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.503416, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DepositResource.php:61", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/DepositResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/DepositResource.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FDepositResource.php&line=61", "ajax": false, "filename": "DepositResource.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 91.394, "width_percent": 3.274}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.5134618, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "WithdrawalResource.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FWithdrawalResource.php&line=49", "ajax": false, "filename": "WithdrawalResource.php", "line": "49"}, "connection": "china15tema2", "explain": null, "start_percent": 94.668, "width_percent": 2.432}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/filament/filament/src/Resources/Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "app/Providers/Filament/AdminPanelProvider.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Providers/Filament/AdminPanelProvider.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 41}], "start": **********.52447, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "WithdrawalResource.php:54", "source": {"index": 16, "namespace": null, "name": "app/Filament/Admin/Resources/WithdrawalResource.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Filament/Admin/Resources/WithdrawalResource.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FFilament%2FAdmin%2FResources%2FWithdrawalResource.php&line=54", "ajax": false, "filename": "WithdrawalResource.php", "line": "54"}, "connection": "china15tema2", "explain": null, "start_percent": 97.1, "width_percent": 2.9}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Wallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": {"app.filament.admin.resources.wallet-resource.pages.edit-wallet #yT9WUTCCt4ZhPOmrtiBZ": "array:4 [\n  \"data\" => array:19 [\n    \"data\" => array:28 [\n      \"id\" => 279\n      \"user_id\" => 17484654\n      \"currency\" => \"BRL\"\n      \"symbol\" => \"R$\"\n      \"balance\" => \"0.00\"\n      \"balance_bonus_rollover\" => \"0.00\"\n      \"balance_deposit_rollover\" => \"0.00\"\n      \"balance_withdrawal\" => \"0.00\"\n      \"balance_bonus\" => \"0.00\"\n      \"balance_cryptocurrency\" => \"0.00000000\"\n      \"balance_demo\" => \"1.00000000\"\n      \"refer_rewards\" => \"0.00\"\n      \"hide_balance\" => 0\n      \"active\" => 1\n      \"created_at\" => \"2024-11-26T22:24:39.000000Z\"\n      \"updated_at\" => \"2024-11-28T19:14:56.000000Z\"\n      \"total_bet\" => \"0.00\"\n      \"total_won\" => 0\n      \"total_lose\" => 0\n      \"last_won\" => 0\n      \"last_lose\" => 0\n      \"vip_level\" => 0\n      \"vip_points\" => 0\n      \"vip_wallet\" => \"0.00\"\n      \"mission_deposit_wallet\" => \"0.00\"\n      \"total_balance\" => 0.0\n      \"total_balance_without_bonus\" => 0.0\n      \"approval_password_save\" => null\n    ]\n    \"previousUrl\" => \"https://job.forradapg.com/ad-min-can-admin/minha-carteira\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Wallet {#2817\n      #connection: \"mysql\"\n      #table: \"wallets\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:25 [\n        \"id\" => 279\n        \"user_id\" => 17484654\n        \"currency\" => \"BRL\"\n        \"symbol\" => \"R$\"\n        \"balance\" => \"0.00\"\n        \"balance_bonus_rollover\" => \"0.00\"\n        \"balance_deposit_rollover\" => \"0.00\"\n        \"balance_withdrawal\" => \"0.00\"\n        \"balance_bonus\" => \"0.00\"\n        \"balance_cryptocurrency\" => \"0.00000000\"\n        \"balance_demo\" => \"1.00000000\"\n        \"refer_rewards\" => \"0.00\"\n        \"hide_balance\" => 0\n        \"active\" => 1\n        \"created_at\" => \"2024-11-26 19:24:39\"\n        \"updated_at\" => \"2024-11-28 16:14:56\"\n        \"total_bet\" => \"0.00\"\n        \"total_won\" => 0\n        \"total_lose\" => 0\n        \"last_won\" => 0\n        \"last_lose\" => 0\n        \"vip_level\" => 0\n        \"vip_points\" => 0\n        \"vip_wallet\" => \"0.00\"\n        \"mission_deposit_wallet\" => \"0.00\"\n      ]\n      #original: array:25 [\n        \"id\" => 279\n        \"user_id\" => 17484654\n        \"currency\" => \"BRL\"\n        \"symbol\" => \"R$\"\n        \"balance\" => \"0.00\"\n        \"balance_bonus_rollover\" => \"0.00\"\n        \"balance_deposit_rollover\" => \"0.00\"\n        \"balance_withdrawal\" => \"0.00\"\n        \"balance_bonus\" => \"0.00\"\n        \"balance_cryptocurrency\" => \"0.00000000\"\n        \"balance_demo\" => \"1.00000000\"\n        \"refer_rewards\" => \"0.00\"\n        \"hide_balance\" => 0\n        \"active\" => 1\n        \"created_at\" => \"2024-11-26 19:24:39\"\n        \"updated_at\" => \"2024-11-28 16:14:56\"\n        \"total_bet\" => \"0.00\"\n        \"total_won\" => 0\n        \"total_lose\" => 0\n        \"last_won\" => 0\n        \"last_lose\" => 0\n        \"vip_level\" => 0\n        \"vip_points\" => 0\n        \"vip_wallet\" => \"0.00\"\n        \"mission_deposit_wallet\" => \"0.00\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: array:2 [\n        0 => \"total_balance\"\n        1 => \"total_balance_without_bonus\"\n      ]\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:21 [\n        0 => \"user_id\"\n        1 => \"currency\"\n        2 => \"symbol\"\n        3 => \"balance\"\n        4 => \"balance_withdrawal\"\n        5 => \"balance_deposit_rollover\"\n        6 => \"balance_bonus\"\n        7 => \"balance_bonus_rollover\"\n        8 => \"balance_cryptocurrency\"\n        9 => \"balance_demo\"\n        10 => \"refer_rewards\"\n        11 => \"total_bet\"\n        12 => \"total_won\"\n        13 => \"total_lose\"\n        14 => \"last_won\"\n        15 => \"last_lose\"\n        16 => \"hide_balance\"\n        17 => \"active\"\n        18 => \"vip_level\"\n        19 => \"vip_points\"\n        20 => \"vip_wallet\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.admin.resources.wallet-resource.pages.edit-wallet\"\n  \"component\" => \"App\\Filament\\Admin\\Resources\\WalletResource\\Pages\\EditWallet\"\n  \"id\" => \"yT9WUTCCt4ZhPOmrtiBZ\"\n]", "filament.livewire.global-search #ryOhzbAkOT3T88cICAoQ": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"ryOhzbAkOT3T88cICAoQ\"\n]", "filament.livewire.notifications #FTH0Bh0rDNHk5BAJrENK": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3750\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"FTH0Bh0rDNHk5BAJrENK\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FolWRu7ETNojsciEWiwvxgelKrYSvdZu8ZgtdZqn", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO", "_previous": "array:1 [\n  \"url\" => \"https://job.forradapg.com/ad-min-can-admin/minha-carteira/279/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "filament": "[]"}, "request": {"path_info": "/ad-min-can-admin/minha-cart<PERSON>/279/edit", "status_code": "<pre class=sf-dump id=sf-dump-472228631 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-472228631\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-538253732 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-538253732\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1590802933 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1590802933\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-582017980 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1453 characters\">_ga=GA1.1.1588727896.1727224672; _ga_ELXYT6N9JP=GS1.1.1727224672.1.1.1727224687.0.0.0; _fbp=fb.1.1727224779412.25516585183138564; _gcl_au=1.1.525778040.1727793989.463541494.1727794072.1727794684; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjIvZmJyQkNJR1lFZHY1VytoTFVYYnc9PSIsInZhbHVlIjoiR0hWWE9pT3phMG0ra0tXNEZ0TWNaUkQxUWY5ODMyNDZaQjAzYzM1a3FITWdybGw3QVozRXc3U1VZL3JOK0xwRDJQVTd1QVN6YmdZZWNIRDhDVXFtcDlhR0JlbWNtQUYydm1DSHhUYlFENmVqdWlOclRKVWlwLzU3a2d2WlpXTjdxaUlxbm1hK29TTk9aQVNraGxJN2x1aGwxYnNPbituMUhsb3BiQWhCV2JSTFZjQlVyOGx3NE55cm9tSnJXMzd6WlFIenRIOUxTRGEvR0wrMXpNaFZheVdZSWpkZ2ZRbFREZDh5bnZrL281bz0iLCJtYWMiOiJhNDI3ODAyM2ViYTU0MWZkMjZkZTIxMDNiZDZhOGQ1MDI1YjVlYWUxMWE1YzlmZDc2YmE4ZGQ1OGEzYjlhMWY3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFxSXh5UFliRndNT0xUUjJVK1NRYmc9PSIsInZhbHVlIjoiZVpqemVrTXhNR1RxOE5TWTBmT1NMSGd4V0NMdWVEUldiVjBuSytmc1VGQkJFdE1BMWpZZkV0emhsRVVqaHNMNGNjSHpBYVZreDh5Zm1ZRnM0WGlTQWRocTJMVTBKeG9kQnpiVmVHNVF2Z2tKL29GdEpHb3c3TGd5S1ZoeGdiaWUiLCJtYWMiOiJjYzBlMjgzOWMzMDgyNTEyMDhkN2IzNTc0MWI5Yjg1NDlmYzczZGVmMmRjZGU2NWI2OWIxNjU2MmI1OGU3YTc2IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6Im1LYUhNZEM2RWNXUStFOThoUC9mRGc9PSIsInZhbHVlIjoiVFhJdUh1MVFpNkxwRllXc2xjVVUyclBLQnozbFhxRFRmME56ODd0TXZ1ZWdWZWlwdUFGVWo3V1h2cmxkdlg4aW11OEdCeXlTN2FDbVhVWW9sYlY0b1VTUmNxQTV3T3o4V3RHMHhuVU15cVYrVXpmOVVONldLdnk4ZlZPcWNnTFgiLCJtYWMiOiIzZDdkM2JiYzM1OWY2MjRhMjdhMjBiNzUxNDg4Y2Q4NDJmNTJjMmU3MWY0OGEyZmU3NDgzYzY2YjhlMTU0OWJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">https://job.forradapg.com/ad-min-can-admin/minha-carteira</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582017980\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1220329875 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_ELXYT6N9JP</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_fbp</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|k6ltzNsW4jF5VozMk8iT6IZZNvamyc7LmcHCDAU4zPjYzTFQWJoTdHwM2NjD|$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FolWRu7ETNojsciEWiwvxgelKrYSvdZu8ZgtdZqn</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DBzCPMApVq0pmypuI8QDLRsFwUspw4wKEldBwf9G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1220329875\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1977921332 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 19:15:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjAzVjZHRHkxQWhwN1hLVUNFM2lwOGc9PSIsInZhbHVlIjoieFZSbXM0ZmphOFcvMGtmT3RLYmJidWs5eSsrYW1Kdld4ZXc0MzYzK2JKbHBvVXZnMndMR3lzQkJhZUZHb3djcGdrV04yamFITERMR2UyMTdnZGpVUkdBcm1vOXlwY3RKZFB1MGo3aHZpbkZ6M3hMZUxWWnBVOGM4dXh4QUtUclQiLCJtYWMiOiI0MWIxYmE2ZjBiMzcwZWMwNDFhOTdlOTQ0OTNiYzUxODYzZmI1NDdkMTU0NGZlMmE2ZjcwYzhlYTkwYmM1YzIwIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 21:15:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6ImVMbk51UzB5Uy9aczE4cUZva0NWSFE9PSIsInZhbHVlIjoiQ2ttcmJONEthS05JSVZmMXBHRnRWS2I1cVVLRUVyUDJPTjF1bzBTMHltY21ndG5aOVp2R0VBekMyUDYvM2VVOGJXNERGdXFvSjVXZGo3am9ZUThMMnA5U3ZnWlRpM28wYXYxTXBNaTRBZXpHU0lRdDVPdzNOVk1aSU1jd3lQbnoiLCJtYWMiOiJhMDdmMTU1OGMwMTY2MDlmNDVhZGI4NTAwMTcyY2Q4OTZlZTgwNWVjNTExNTEzMWY2ZmI5NGUwM2NkMjUyOGIwIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 21:15:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjAzVjZHRHkxQWhwN1hLVUNFM2lwOGc9PSIsInZhbHVlIjoieFZSbXM0ZmphOFcvMGtmT3RLYmJidWs5eSsrYW1Kdld4ZXc0MzYzK2JKbHBvVXZnMndMR3lzQkJhZUZHb3djcGdrV04yamFITERMR2UyMTdnZGpVUkdBcm1vOXlwY3RKZFB1MGo3aHZpbkZ6M3hMZUxWWnBVOGM4dXh4QUtUclQiLCJtYWMiOiI0MWIxYmE2ZjBiMzcwZWMwNDFhOTdlOTQ0OTNiYzUxODYzZmI1NDdkMTU0NGZlMmE2ZjcwYzhlYTkwYmM1YzIwIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 21:15:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6ImVMbk51UzB5Uy9aczE4cUZva0NWSFE9PSIsInZhbHVlIjoiQ2ttcmJONEthS05JSVZmMXBHRnRWS2I1cVVLRUVyUDJPTjF1bzBTMHltY21ndG5aOVp2R0VBekMyUDYvM2VVOGJXNERGdXFvSjVXZGo3am9ZUThMMnA5U3ZnWlRpM28wYXYxTXBNaTRBZXpHU0lRdDVPdzNOVk1aSU1jd3lQbnoiLCJtYWMiOiJhMDdmMTU1OGMwMTY2MDlmNDVhZGI4NTAwMTcyY2Q4OTZlZTgwNWVjNTExNTEzMWY2ZmI5NGUwM2NkMjUyOGIwIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 21:15:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977921332\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-304576578 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FolWRu7ETNojsciEWiwvxgelKrYSvdZu8ZgtdZqn</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2b$12$hiF5iZBk7nYwUcGx3akCTOoI2FiltmD/ojHJzvT0Q6G2l6jUQ1VvO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"66 characters\">https://job.forradapg.com/ad-min-can-admin/minha-carteira/279/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-304576578\", {\"maxDepth\":0})</script>\n"}}