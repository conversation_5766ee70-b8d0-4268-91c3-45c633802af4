{"__meta": {"id": "Xf6909c6cc767e0a8ebaf549fd446b49f", "datetime": "2024-11-28 16:02:59", "utime": 1732820579.868284, "method": "GET", "uri": "/api/games/all", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732820562.223079, "end": 1732820579.868301, "duration": 17.645221948623657, "duration_str": "17.65s", "measures": [{"label": "Booting", "start": 1732820562.223079, "relative_start": 0, "end": 1732820566.806986, "relative_end": 1732820566.806986, "duration": 4.583907127380371, "duration_str": "4.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732820566.807006, "relative_start": 4.5839269161224365, "end": 1732820579.868303, "relative_end": 2.1457672119140625e-06, "duration": 13.061297178268433, "duration_str": "13.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 15153368, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/games/all", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@index", "namespace": null, "prefix": "api/games", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=44\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:44-53</a>"}, "queries": {"nb_statements": 1023, "nb_visible_statements": 500, "nb_excluded_statements": 523, "nb_failed_statements": 0, "accumulated_duration": 1.5746700000000016, "accumulated_duration_str": "1.57s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `providers` where exists (select * from `games` where `providers`.`id` = `games`.`provider_id` and `show_home` = 1 and `status` = 1) and `status` = 1 order by `name` desc", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.029712, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "GameController.php:50", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=50", "ajax": false, "filename": "GameController.php", "line": "50"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 0.213}, {"sql": "select * from `games` where `show_home` = 1 and `status` = 1 and `games`.`provider_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 11126) order by `views` desc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.18598, "duration": 0.01467, "duration_str": "14.67ms", "memory": 0, "memory_str": null, "filename": "GameController.php:50", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=50", "ajax": false, "filename": "GameController.php", "line": "50"}, "connection": "china15tema2", "explain": null, "start_percent": 0.213, "width_percent": 0.932}, {"sql": "select * from `providers` where `providers`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 11126)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.32247, "duration": 0.01622, "duration_str": "16.22ms", "memory": 0, "memory_str": null, "filename": "GameController.php:50", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=50", "ajax": false, "filename": "GameController.php", "line": "50"}, "connection": "china15tema2", "explain": null, "start_percent": 1.145, "width_percent": 1.03}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 828 limit 1", "type": "query", "params": [], "bindings": [********, 828], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.829814, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 2.175, "width_percent": 0.098}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 828 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [828], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.853099, "duration": 0.019260000000000003, "duration_str": "19.26ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 2.273, "width_percent": 1.223}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 828 limit 1", "type": "query", "params": [], "bindings": [********, 828], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.891948, "duration": 0.01794, "duration_str": "17.94ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 3.496, "width_percent": 1.139}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 828 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [828], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.929195, "duration": 0.01849, "duration_str": "18.49ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 4.635, "width_percent": 1.174}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 841 limit 1", "type": "query", "params": [], "bindings": [********, 841], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.971412, "duration": 0.012119999999999999, "duration_str": "12.12ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 5.809, "width_percent": 0.77}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 841 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [841], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.0147688, "duration": 0.0111, "duration_str": "11.1ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 6.579, "width_percent": 0.705}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 841 limit 1", "type": "query", "params": [], "bindings": [********, 841], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.0362692, "duration": 0.01782, "duration_str": "17.82ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 7.284, "width_percent": 1.132}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 841 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [841], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.084068, "duration": 0.01788, "duration_str": "17.88ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 8.416, "width_percent": 1.135}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 824 limit 1", "type": "query", "params": [], "bindings": [********, 824], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.123317, "duration": 0.00975, "duration_str": "9.75ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 9.551, "width_percent": 0.619}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 824 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [824], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.1678069, "duration": 0.013710000000000002, "duration_str": "13.71ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 10.17, "width_percent": 0.871}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 824 limit 1", "type": "query", "params": [], "bindings": [********, 824], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.2020261, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 11.041, "width_percent": 0.02}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 824 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [824], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.238246, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 11.061, "width_percent": 0.135}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 844 limit 1", "type": "query", "params": [], "bindings": [********, 844], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.264811, "duration": 0.01715, "duration_str": "17.15ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 11.196, "width_percent": 1.089}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 844 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [844], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.3044152, "duration": 0.02116, "duration_str": "21.16ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 12.285, "width_percent": 1.344}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 844 limit 1", "type": "query", "params": [], "bindings": [********, 844], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.3528109, "duration": 0.005, "duration_str": "5ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 13.629, "width_percent": 0.318}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 844 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [844], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.380807, "duration": 0.01475, "duration_str": "14.75ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 13.946, "width_percent": 0.937}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 851 limit 1", "type": "query", "params": [], "bindings": [********, 851], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.416197, "duration": 0.01574, "duration_str": "15.74ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 14.883, "width_percent": 1}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 851 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [851], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.466898, "duration": 0.0056, "duration_str": "5.6ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 15.883, "width_percent": 0.356}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 851 limit 1", "type": "query", "params": [], "bindings": [********, 851], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4809692, "duration": 0.01696, "duration_str": "16.96ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 16.238, "width_percent": 1.077}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 851 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [851], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.5338929, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 17.315, "width_percent": 0.226}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 830 limit 1", "type": "query", "params": [], "bindings": [********, 830], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.564024, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 17.541, "width_percent": 0.029}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 830 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [830], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.5949252, "duration": 0.00782, "duration_str": "7.82ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 17.571, "width_percent": 0.497}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 830 limit 1", "type": "query", "params": [], "bindings": [********, 830], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.623164, "duration": 0.0066500000000000005, "duration_str": "6.65ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 18.067, "width_percent": 0.422}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 830 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [830], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.6554658, "duration": 0.01041, "duration_str": "10.41ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 18.49, "width_percent": 0.661}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 831 limit 1", "type": "query", "params": [], "bindings": [********, 831], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.689156, "duration": 0.01366, "duration_str": "13.66ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 19.151, "width_percent": 0.867}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 831 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [831], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.73106, "duration": 0.00276, "duration_str": "2.76ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 20.018, "width_percent": 0.175}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 831 limit 1", "type": "query", "params": [], "bindings": [********, 831], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.752511, "duration": 0.01331, "duration_str": "13.31ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 20.193, "width_percent": 0.845}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 831 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [831], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.78408, "duration": 0.01775, "duration_str": "17.75ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 21.039, "width_percent": 1.127}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1195 limit 1", "type": "query", "params": [], "bindings": [********, 1195], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.830549, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 22.166, "width_percent": 0.213}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1195 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.849395, "duration": 0.02456, "duration_str": "24.56ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 22.379, "width_percent": 1.56}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1195 limit 1", "type": "query", "params": [], "bindings": [********, 1195], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.881085, "duration": 0.020399999999999998, "duration_str": "20.4ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 23.939, "width_percent": 1.296}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1195 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.9349601, "duration": 0.01103, "duration_str": "11.03ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 25.234, "width_percent": 0.7}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 397 limit 1", "type": "query", "params": [], "bindings": [********, 397], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.963661, "duration": 0.0129, "duration_str": "12.9ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 25.935, "width_percent": 0.819}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 397 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [397], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.01722, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 26.754, "width_percent": 0.23}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 397 limit 1", "type": "query", "params": [], "bindings": [********, 397], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.031422, "duration": 0.01604, "duration_str": "16.04ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 26.984, "width_percent": 1.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 397 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [397], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.079349, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 28.003, "width_percent": 0.157}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1194 limit 1", "type": "query", "params": [], "bindings": [********, 1194], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.114906, "duration": 0.012580000000000001, "duration_str": "12.58ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 28.16, "width_percent": 0.799}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1194 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1194], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.162046, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 28.959, "width_percent": 0.235}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1194 limit 1", "type": "query", "params": [], "bindings": [********, 1194], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.179992, "duration": 0.00968, "duration_str": "9.68ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 29.194, "width_percent": 0.615}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1194 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1194], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.219465, "duration": 0.00638, "duration_str": "6.38ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 29.809, "width_percent": 0.405}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1190 limit 1", "type": "query", "params": [], "bindings": [********, 1190], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.24423, "duration": 0.013619999999999998, "duration_str": "13.62ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 30.214, "width_percent": 0.865}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1190 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1190], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.286675, "duration": 0.0072699999999999996, "duration_str": "7.27ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 31.079, "width_percent": 0.462}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1190 limit 1", "type": "query", "params": [], "bindings": [********, 1190], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.3196619, "duration": 0.01059, "duration_str": "10.59ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 31.541, "width_percent": 0.673}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1190 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1190], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.366589, "duration": 0.01139, "duration_str": "11.39ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 32.213, "width_percent": 0.723}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1193 limit 1", "type": "query", "params": [], "bindings": [********, 1193], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4038641, "duration": 0.0166, "duration_str": "16.6ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 32.936, "width_percent": 1.054}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1193 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1193], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4472048, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 33.991, "width_percent": 0.25}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1193 limit 1", "type": "query", "params": [], "bindings": [********, 1193], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.473132, "duration": 0.01281, "duration_str": "12.81ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 34.24, "width_percent": 0.814}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1193 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1193], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.504405, "duration": 0.01719, "duration_str": "17.19ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 35.054, "width_percent": 1.092}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1192 limit 1", "type": "query", "params": [], "bindings": [********, 1192], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.5469732, "duration": 0.00501, "duration_str": "5.01ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 36.145, "width_percent": 0.318}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1192 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1192], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.572258, "duration": 0.00929, "duration_str": "9.29ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 36.464, "width_percent": 0.59}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1192 limit 1", "type": "query", "params": [], "bindings": [********, 1192], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.597722, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 37.053, "width_percent": 0.158}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1192 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1192], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.635045, "duration": 0.00887, "duration_str": "8.87ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 37.212, "width_percent": 0.563}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1191 limit 1", "type": "query", "params": [], "bindings": [********, 1191], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.685993, "duration": 0.0079, "duration_str": "7.9ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 37.775, "width_percent": 0.502}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1191 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1191], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.7417731, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 38.277, "width_percent": 0.131}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1191 limit 1", "type": "query", "params": [], "bindings": [********, 1191], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.784018, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 38.407, "width_percent": 0.114}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1191 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1191], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.812449, "duration": 0.03352, "duration_str": "33.52ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 38.522, "width_percent": 2.129}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1060 limit 1", "type": "query", "params": [], "bindings": [********, 1060], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8689952, "duration": 0.027329999999999997, "duration_str": "27.33ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 40.65, "width_percent": 1.736}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1060 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1060], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.938344, "duration": 0.00985, "duration_str": "9.85ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 42.386, "width_percent": 0.626}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1060 limit 1", "type": "query", "params": [], "bindings": [********, 1060], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.9855158, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 43.012, "width_percent": 0.094}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1060 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1060], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.03929, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 43.106, "width_percent": 0.154}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1066 limit 1", "type": "query", "params": [], "bindings": [********, 1066], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.088279, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 43.259, "width_percent": 0.09}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1066 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1066], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.1196141, "duration": 0.01069, "duration_str": "10.69ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 43.349, "width_percent": 0.679}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1066 limit 1", "type": "query", "params": [], "bindings": [********, 1066], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.16715, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 44.028, "width_percent": 0.202}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1066 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1066], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.203773, "duration": 0.01421, "duration_str": "14.21ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 44.23, "width_percent": 0.902}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1059 limit 1", "type": "query", "params": [], "bindings": [********, 1059], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.255739, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 45.133, "width_percent": 0.124}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1059 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1059], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.269201, "duration": 0.020649999999999998, "duration_str": "20.65ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 45.256, "width_percent": 1.311}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1059 limit 1", "type": "query", "params": [], "bindings": [********, 1059], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.3141909, "duration": 0.00601, "duration_str": "6.01ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 46.568, "width_percent": 0.382}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1059 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1059], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.3462622, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 46.95, "width_percent": 0.218}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1062 limit 1", "type": "query", "params": [], "bindings": [********, 1062], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.367641, "duration": 0.00657, "duration_str": "6.57ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 47.167, "width_percent": 0.417}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1062 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1062], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4019818, "duration": 0.0048, "duration_str": "4.8ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 47.585, "width_percent": 0.305}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1062 limit 1", "type": "query", "params": [], "bindings": [********, 1062], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.42882, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 47.889, "width_percent": 0.149}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1062 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1062], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.45902, "duration": 0.01089, "duration_str": "10.89ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 48.039, "width_percent": 0.692}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1065 limit 1", "type": "query", "params": [], "bindings": [********, 1065], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.4956272, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 48.73, "width_percent": 0.174}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1065 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1065], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.5203202, "duration": 0.01239, "duration_str": "12.39ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 48.904, "width_percent": 0.787}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1065 limit 1", "type": "query", "params": [], "bindings": [********, 1065], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.536972, "duration": 0.01653, "duration_str": "16.53ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 49.691, "width_percent": 1.05}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1065 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1065], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.586368, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 50.741, "width_percent": 0.207}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1075 limit 1", "type": "query", "params": [], "bindings": [********, 1075], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.6107678, "duration": 0.01076, "duration_str": "10.76ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 50.948, "width_percent": 0.683}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1075 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1075], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.636533, "duration": 0.01695, "duration_str": "16.95ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 51.631, "width_percent": 1.076}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1075 limit 1", "type": "query", "params": [], "bindings": [********, 1075], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.671405, "duration": 0.01214, "duration_str": "12.14ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 52.708, "width_percent": 0.771}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1075 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1075], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.693381, "duration": 0.014539999999999999, "duration_str": "14.54ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 53.479, "width_percent": 0.923}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1076 limit 1", "type": "query", "params": [], "bindings": [********, 1076], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.7370498, "duration": 0.00744, "duration_str": "7.44ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 54.402, "width_percent": 0.472}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1076 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1076], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.765569, "duration": 0.008289999999999999, "duration_str": "8.29ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 54.874, "width_percent": 0.526}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1076 limit 1", "type": "query", "params": [], "bindings": [********, 1076], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8017561, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 55.401, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1076 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1076], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.827017, "duration": 0.00679, "duration_str": "6.79ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 55.429, "width_percent": 0.431}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 811 limit 1", "type": "query", "params": [], "bindings": [********, 811], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.85662, "duration": 0.0049299999999999995, "duration_str": "4.93ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 55.86, "width_percent": 0.313}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 811 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [811], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.8944259, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 56.173, "width_percent": 0.035}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 811 limit 1", "type": "query", "params": [], "bindings": [********, 811], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.9156852, "duration": 0.00924, "duration_str": "9.24ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 56.208, "width_percent": 0.587}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 811 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [811], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.961733, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 56.795, "width_percent": 0.031}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 806 limit 1", "type": "query", "params": [], "bindings": [********, 806], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.9792452, "duration": 0.0087, "duration_str": "8.7ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 56.826, "width_percent": 0.552}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 806 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [806], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.004971, "duration": 0.0165, "duration_str": "16.5ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 57.378, "width_percent": 1.048}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 806 limit 1", "type": "query", "params": [], "bindings": [********, 806], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.046185, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 58.426, "width_percent": 0.032}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 806 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [806], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.056619, "duration": 0.016649999999999998, "duration_str": "16.65ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 58.458, "width_percent": 1.057}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 807 limit 1", "type": "query", "params": [], "bindings": [********, 807], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.107609, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 59.515, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 807 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [807], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.138231, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 59.546, "width_percent": 0.225}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 807 limit 1", "type": "query", "params": [], "bindings": [********, 807], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.16684, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 59.771, "width_percent": 0.03}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 807 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [807], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 38, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 41, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.191342, "duration": 0.01018, "duration_str": "10.18ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 59.802, "width_percent": 0.646}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 766 limit 1", "type": "query", "params": [], "bindings": [********, 766], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 35, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}, {"index": 37, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 102}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 52}], "start": **********.2267969, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 60.448, "width_percent": 0.136}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2618551, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.584, "width_percent": 0.23}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.26569, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.814, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2661831, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.833, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.267327, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.851, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2677732, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.869, "width_percent": 0.106}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.269601, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 60.975, "width_percent": 0.096}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.271291, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.071, "width_percent": 0.153}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.274508, "duration": 0.00646, "duration_str": "6.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.224, "width_percent": 0.41}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2812068, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.635, "width_percent": 0.016}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.281599, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.65, "width_percent": 0.014}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2819762, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.664, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.28304, "duration": 0.01056, "duration_str": "10.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 61.681, "width_percent": 0.671}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.293928, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.352, "width_percent": 0.022}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.294455, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.374, "width_percent": 0.202}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.297804, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.576, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.29879, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.595, "width_percent": 0.172}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3016622, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.767, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.302113, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.785, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.302552, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.802, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.303471, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.817, "width_percent": 0.078}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3048248, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.895, "width_percent": 0.039}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3055658, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 62.934, "width_percent": 0.183}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3086169, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.117, "width_percent": 0.149}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.311638, "duration": 0.0045, "duration_str": "4.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.266, "width_percent": 0.286}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3163302, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.552, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.316756, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.569, "width_percent": 0.055}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3177888, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.624, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.318761, "duration": 0.006809999999999999, "duration_str": "6.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 63.639, "width_percent": 0.432}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.325808, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.072, "width_percent": 0.257}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.330137, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.329, "width_percent": 0.227}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.333959, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.556, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3351119, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.577, "width_percent": 0.161}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3378282, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.738, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.338272, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.756, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.338738, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.775, "width_percent": 0.016}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.339607, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.791, "width_percent": 0.12}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.341643, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 64.911, "width_percent": 0.096}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.343314, "duration": 0.0065, "duration_str": "6.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.007, "width_percent": 0.413}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.35001, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.42, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.350937, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.435, "width_percent": 0.171}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.353797, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.607, "width_percent": 0.188}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.356913, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.795, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3573651, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.812, "width_percent": 0.021}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3583388, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.833, "width_percent": 0.104}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3601391, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.936, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3605368, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.954, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.360933, "duration": 0.00856, "duration_str": "8.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 65.969, "width_percent": 0.544}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.370217, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.512, "width_percent": 0.121}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.372281, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.634, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.372701, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.651, "width_percent": 0.056}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3737059, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.707, "width_percent": 0.023}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.37996, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.731, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3803859, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.748, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.380776, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.764, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3811529, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.777, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3819818, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.79, "width_percent": 0.016}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3823729, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.806, "width_percent": 0.013}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.38268, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.818, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.383052, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.832, "width_percent": 0.022}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.38387, "duration": 0.00811, "duration_str": "8.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 66.854, "width_percent": 0.515}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392238, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.369, "width_percent": 0.246}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.396285, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.615, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3966238, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.628, "width_percent": 0.014}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397749, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.641, "width_percent": 0.246}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4017892, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.887, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402243, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.906, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402591, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.92, "width_percent": 0.011}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403364, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 67.932, "width_percent": 0.143}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.405809, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.075, "width_percent": 0.016}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406228, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.09, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406645, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.108, "width_percent": 0.14}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411106, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.249, "width_percent": 0.163}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4138181, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.412, "width_percent": 0.242}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417762, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.654, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.418151, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.668, "width_percent": 0.146}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.424801, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.814, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.425322, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 68.836, "width_percent": 0.209}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.428814, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.045, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4293818, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.069, "width_percent": 0.07}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.431154, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.139, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4317071, "duration": 0.006059999999999999, "duration_str": "6.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.162, "width_percent": 0.385}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43798, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.547, "width_percent": 0.169}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4408388, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.716, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4418612, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.733, "width_percent": 0.241}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.445847, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 69.974, "width_percent": 0.171}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.448692, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.145, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.449194, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.163, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.450098, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.179, "width_percent": 0.227}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.453828, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.405, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.454267, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.422, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.454751, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.441, "width_percent": 0.019}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4556499, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.46, "width_percent": 0.238}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4595149, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.697, "width_percent": 0.013}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4598339, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.71, "width_percent": 0.058}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4608798, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.768, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.461629, "duration": 0.00813, "duration_str": "8.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 70.784, "width_percent": 0.516}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.469904, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.3, "width_percent": 0.082}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4713168, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.382, "width_percent": 0.138}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.473617, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.52, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.474354, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.533, "width_percent": 0.208}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4778018, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.741, "width_percent": 0.015}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4781878, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.757, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.478567, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.772, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.479378, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.787, "width_percent": 0.101}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4811578, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.888, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4815578, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.905, "width_percent": 0.016}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4819448, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.92, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.482722, "duration": 0.00846, "duration_str": "8.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 71.937, "width_percent": 0.537}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4914181, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.474, "width_percent": 0.142}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.49382, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.616, "width_percent": 0.014}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.494191, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.63, "width_percent": 0.014}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.494984, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.644, "width_percent": 0.128}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.497142, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.772, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4975562, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.79, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.497972, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 72.807, "width_percent": 0.198}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.50164, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.005, "width_percent": 0.253}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5058022, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.258, "width_percent": 0.207}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.509228, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.465, "width_percent": 0.02}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.509697, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.485, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.510665, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.501, "width_percent": 0.193}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5138922, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.694, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.514324, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.712, "width_percent": 0.21}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.517784, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.922, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.518637, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 73.939, "width_percent": 0.166}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.521447, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.105, "width_percent": 0.257}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.525663, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.362, "width_percent": 0.016}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.526071, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.378, "width_percent": 0.225}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5304332, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.603, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.530873, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.62, "width_percent": 0.02}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5313132, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.64, "width_percent": 0.14}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.533656, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.78, "width_percent": 0.194}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5372238, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.974, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.537577, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 74.988, "width_percent": 0.015}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.537926, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.004, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.538268, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.018, "width_percent": 0.046}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.539466, "duration": 0.00596, "duration_str": "5.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.064, "width_percent": 0.378}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.545556, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.442, "width_percent": 0.035}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5462189, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.477, "width_percent": 0.214}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5497248, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.691, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.550572, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.707, "width_percent": 0.06}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.551693, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.767, "width_percent": 0.157}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.554325, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 75.924, "width_percent": 0.211}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.557854, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.135, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5588171, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.151, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5592709, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.169, "width_percent": 0.15}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.561784, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.319, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.565592, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.332, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.566657, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.351, "width_percent": 0.192}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.569869, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.542, "width_percent": 0.091}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.571437, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.633, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.573726, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.77, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.574492, "duration": 0.00314, "duration_str": "3.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.784, "width_percent": 0.199}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5777829, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.983, "width_percent": 0.013}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.57812, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 76.996, "width_percent": 0.161}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.580792, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.156, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.581508, "duration": 0.0072699999999999996, "duration_str": "7.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.17, "width_percent": 0.462}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.588982, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.632, "width_percent": 0.02}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.589484, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.651, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.589899, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.67, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.590794, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.687, "width_percent": 0.185}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.593898, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.872, "width_percent": 0.019}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5943332, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.891, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.594787, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 77.91, "width_percent": 0.182}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5984151, "duration": 0.00327, "duration_str": "3.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.092, "width_percent": 0.208}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6018848, "duration": 0.0076100000000000004, "duration_str": "7.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.3, "width_percent": 0.483}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.60976, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.783, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.610218, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.8, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.611495, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.818, "width_percent": 0.138}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.613883, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.956, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.614343, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 78.975, "width_percent": 0.211}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6178372, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.185, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.618916, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.203, "width_percent": 0.174}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.629535, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.377, "width_percent": 0.027}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6301851, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.403, "width_percent": 0.02}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.630666, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.423, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.631604, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.438, "width_percent": 0.036}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.632341, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.473, "width_percent": 0.079}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.633733, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.552, "width_percent": 0.237}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6376472, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.789, "width_percent": 0.019}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.638666, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.808, "width_percent": 0.105}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.640478, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.913, "width_percent": 0.016}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.640876, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 79.928, "width_percent": 0.086}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.642367, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.014, "width_percent": 0.016}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6431592, "duration": 0.00907, "duration_str": "9.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.03, "width_percent": 0.576}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.652509, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.606, "width_percent": 0.019}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6529741, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.625, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6534588, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.644, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.654339, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.659, "width_percent": 0.206}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.657723, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 80.865, "width_percent": 0.248}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.661767, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.114, "width_percent": 0.014}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662153, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.127, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.663009, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.146, "width_percent": 0.167}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6657982, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.313, "width_percent": 0.019}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.66624, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.332, "width_percent": 0.214}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6697528, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.546, "width_percent": 0.014}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.670639, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.56, "width_percent": 0.048}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.671555, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.608, "width_percent": 0.057}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6725671, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.665, "width_percent": 0.085}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674031, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.75, "width_percent": 0.014}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674707, "duration": 0.00796, "duration_str": "7.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 81.764, "width_percent": 0.506}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.682913, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.269, "width_percent": 0.022}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.683445, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.292, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6839168, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.311, "width_percent": 0.138}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.687914, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.448, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.688288, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.464, "width_percent": 0.013}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.688627, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.477, "width_percent": 0.075}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6899588, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.552, "width_percent": 0.172}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.693355, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 82.724, "width_percent": 0.279}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.697973, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.003, "width_percent": 0.292}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.702759, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.296, "width_percent": 0.184}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7058449, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.48, "width_percent": 0.014}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.706659, "duration": 0.00283, "duration_str": "2.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.494, "width_percent": 0.18}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7096438, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.673, "width_percent": 0.015}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.710006, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.688, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.710378, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.703, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.711326, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.717, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7117, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.732, "width_percent": 0.067}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.712869, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.799, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7132292, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.813, "width_percent": 0.096}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.715303, "duration": 0.00636, "duration_str": "6.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 83.909, "width_percent": 0.404}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.721833, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.313, "width_percent": 0.013}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.722175, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.326, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.722545, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.341, "width_percent": 0.194}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7262452, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.535, "width_percent": 0.207}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.729652, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.742, "width_percent": 0.213}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.733209, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 84.955, "width_percent": 0.269}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.737682, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.224, "width_percent": 0.022}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7387931, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.246, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.739249, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.264, "width_percent": 0.151}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7418022, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.415, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7422478, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.433, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.743248, "duration": 0.00649, "duration_str": "6.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.45, "width_percent": 0.412}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.749963, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.862, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.750377, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.88, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.750742, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 85.895, "width_percent": 0.149}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.757891, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.044, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.758407, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.067, "width_percent": 0.207}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7618098, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.274, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7621849, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.289, "width_percent": 0.016}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765524, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.305, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7659888, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.326, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.76637, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.343, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766698, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.356, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.76749, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.372, "width_percent": 0.135}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7697349, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.507, "width_percent": 0.013}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.770072, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.52, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.770391, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.533, "width_percent": 0.071}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.772176, "duration": 0.00954, "duration_str": "9.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 86.604, "width_percent": 0.606}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7819371, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.21, "width_percent": 0.089}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.783505, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.299, "width_percent": 0.014}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.783855, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.313, "width_percent": 0.11}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.786232, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.423, "width_percent": 0.217}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7897859, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.639, "width_percent": 0.013}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.790156, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.653, "width_percent": 0.014}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.790515, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.667, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.791259, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.68, "width_percent": 0.142}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.793659, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.822, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7940779, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 87.839, "width_percent": 0.225}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.797769, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.064, "width_percent": 0.012}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.798501, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.076, "width_percent": 0.191}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.801682, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.267, "width_percent": 0.115}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.803624, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.382, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8039849, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.397, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.804692, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.41, "width_percent": 0.051}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.805634, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.461, "width_percent": 0.153}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.808199, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.614, "width_percent": 0.095}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8115451, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.709, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.812532, "duration": 0.00512, "duration_str": "5.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 88.727, "width_percent": 0.325}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.817858, "duration": 0.005679999999999999, "duration_str": "5.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.052, "width_percent": 0.361}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.823701, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.412, "width_percent": 0.014}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.824068, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.426, "width_percent": 0.02}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8249838, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.447, "width_percent": 0.293}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8297498, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.739, "width_percent": 0.014}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8301198, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.753, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.830493, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.769, "width_percent": 0.014}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.831277, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.783, "width_percent": 0.15}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.83377, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.932, "width_percent": 0.013}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.834127, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.946, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.834447, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.959, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.835107, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 89.971, "width_percent": 0.043}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.835932, "duration": 0.00549, "duration_str": "5.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.014, "width_percent": 0.349}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.841579, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.362, "width_percent": 0.144}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.843983, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.506, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.844791, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.521, "width_percent": 0.05}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.845701, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.57, "width_percent": 0.238}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.849555, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.808, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8498762, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.82, "width_percent": 0.107}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8520238, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 90.928, "width_percent": 0.092}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8536081, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.02, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.854023, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.037, "width_percent": 0.014}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8543909, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.051, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.855007, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.064, "width_percent": 0.099}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.856691, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.163, "width_percent": 0.057}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.857689, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.22, "width_percent": 0.248}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.861713, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.467, "width_percent": 0.011}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8624132, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.479, "width_percent": 0.198}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.86565, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.676, "width_percent": 0.015}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.866, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.691, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8663292, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.704, "width_percent": 0.218}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8704, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 91.922, "width_percent": 0.235}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.874249, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.157, "width_percent": 0.015}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8746078, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.172, "width_percent": 0.183}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8776479, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.355, "width_percent": 0.016}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8785279, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.37, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8789341, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.387, "width_percent": 0.128}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.881105, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.515, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8835518, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.53, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.889208, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.545, "width_percent": 0.02}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.889672, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.565, "width_percent": 0.178}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8926442, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.743, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8931231, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.762, "width_percent": 0.03}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.897312, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.792, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.897707, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.809, "width_percent": 0.011}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.898011, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.82, "width_percent": 0.014}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.89837, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.834, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.899101, "duration": 0.00641, "duration_str": "6.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 92.847, "width_percent": 0.407}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.90568, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.254, "width_percent": 0.014}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.906028, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.268, "width_percent": 0.165}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9087698, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.433, "width_percent": 0.014}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.911809, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.447, "width_percent": 0.016}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.912181, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.463, "width_percent": 0.012}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.912483, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.475, "width_percent": 0.07}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.913727, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.545, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.91453, "duration": 0.00694, "duration_str": "6.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 93.559, "width_percent": 0.441}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.921603, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.922023, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.017, "width_percent": 0.177}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.924976, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.194, "width_percent": 0.018}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.926003, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.212, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9264479, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.231, "width_percent": 0.016}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9268441, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.247, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927306, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.266, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.928136, "duration": 0.0055899999999999995, "duration_str": "5.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.281, "width_percent": 0.355}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.933902, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.636, "width_percent": 0.02}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9343772, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.657, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.934815, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.674, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935664, "duration": 0.00598, "duration_str": "5.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 94.691, "width_percent": 0.38}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9418142, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.071, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9422328, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.088, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9426029, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.103, "width_percent": 0.015}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.943406, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.118, "width_percent": 0.088}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.944938, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.206, "width_percent": 0.013}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.945257, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.219, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9455779, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.233, "width_percent": 0.016}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9463499, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.249, "width_percent": 0.206}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.949714, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.454, "width_percent": 0.012}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950063, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.466, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950522, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.485, "width_percent": 0.016}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.951366, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.501, "width_percent": 0.227}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.955122, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.727, "width_percent": 0.017}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.955514, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.744, "width_percent": 0.126}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9576561, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.87, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.958727, "duration": 0.00566, "duration_str": "5.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 95.887, "width_percent": 0.359}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.964556, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.247, "width_percent": 0.02}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.965028, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.267, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.969498, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.285, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.970317, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.298, "width_percent": 0.016}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.970696, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.314, "width_percent": 0.014}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9710588, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.328, "width_percent": 0.016}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.971535, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.344, "width_percent": 0.13}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.974145, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.474, "width_percent": 0.053}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9751651, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.528, "width_percent": 0.156}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977784, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.683, "width_percent": 0.017}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.978206, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.7, "width_percent": 0.016}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.979158, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.716, "width_percent": 0.158}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9818091, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.874, "width_percent": 0.02}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.982251, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.893, "width_percent": 0.065}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.983422, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 96.959, "width_percent": 0.111}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.985906, "duration": 0.00791, "duration_str": "7.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.07, "width_percent": 0.502}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.99408, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.572, "width_percent": 0.074}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.995425, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.646, "width_percent": 0.014}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.995791, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.66, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9968312, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.676, "width_percent": 0.253}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.0011082, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.929, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.001554, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.948, "width_percent": 0.016}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.001926, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.963, "width_percent": 0.017}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.002858, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 97.98, "width_percent": 0.154}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.005441, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.134, "width_percent": 0.014}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.0057821, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.148, "width_percent": 0.241}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.009712, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.389, "width_percent": 0.022}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.01172, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.411, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.0120578, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.424, "width_percent": 0.016}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.012423, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.44, "width_percent": 0.013}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.012765, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.453, "width_percent": 0.043}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.013891, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.496, "width_percent": 0.239}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.0178301, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.735, "width_percent": 0.018}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.018266, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.753, "width_percent": 0.015}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.0186229, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.769, "width_percent": 0.011}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.01932, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.78, "width_percent": 0.143}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.021693, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.923, "width_percent": 0.011}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.021981, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 98.934, "width_percent": 0.222}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.025622, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.157, "width_percent": 0.016}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.026419, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.173, "width_percent": 0.145}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.028847, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.318, "width_percent": 0.016}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.029217, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.334, "width_percent": 0.018}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.029633, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.352, "width_percent": 0.251}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.034192, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.603, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = ? and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.034646, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.624, "width_percent": 0.013}, {"sql": "select * from `game_likes` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.034996, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.637, "width_percent": 0.166}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = ? and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.037738, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.803, "width_percent": 0.013}, {"sql": "select * from `game_favorites` where `user_id` = ? and `game_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1732820579.03868, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "china15tema2", "explain": null, "start_percent": 99.816, "width_percent": 0.184}, {"sql": "... 523 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"App\\Models\\Game": {"value": 255, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=1", "ajax": false, "filename": "Game.php", "line": "?"}}, "App\\Models\\Provider": {"value": 36, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FProvider.php&line=1", "ajax": false, "filename": "Provider.php", "line": "?"}}}, "count": 291, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/games/all", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-800320061 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-800320061\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IlkvT3NobFZPNU04dXhYNUdHUEYwUHc9PSIsInZhbHVlIjoidUNYR2VKV3NSUzJsSlYvSDJWOXZ2NVpVcjBsellkZmxHVVZmVHFYMERtVjg3YldFUzhVb29yVmJLdkszWFRlUGY4Yll0UFczbThDUlR6WXhhYU1KeWJoL3JubXB1N2pZRUt6VWZkemgxTnZkMFZOVWhKQ1BGcjRhdlBoT2xxNjUiLCJtYWMiOiI2MjA1ZjU4YjhiMjRkMWNmNzJhYTNkMjJjNGRiYWEzNWUyOWY5ZDlhODJjMjZiMzBhOGRjNjJiZDhkZjc2NDY5IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6IlNGOU5NZ2gxL1FWUkZGc2EzRkdkbUE9PSIsInZhbHVlIjoiSVRuT0FPazRVdFFBNHJqbFM4Yjh0M2xMbWhIYWJrV0ViRnZvNFppdnBpcUh3QVRMdWZlTm50Vk9tRXdmYmJkbGhJSlpCaGpSOUt5YWt0eHVteUJWYU5FNEI2c2JDTm01Vk9pNzFrbmRlTzlUL2d3aTdKVHFxL1JwMnB0V091RG8iLCJtYWMiOiJkNjRmY2E3M2NmYWMwMTJjZDRhOTI2MGIyNTNkODc1OTRlOGMxMzIyYjlhZWIwZWM0YjYyM2EzYjZlOTkxMjM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"133 characters\">Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlkvT3NobFZPNU04dXhYNUdHUEYwUHc9PSIsInZhbHVlIjoidUNYR2VKV3NSUzJsSlYvSDJWOXZ2NVpVcjBsellkZmxHVVZmVHFYMERtVjg3YldFUzhVb29yVmJLdkszWFRlUGY4Yll0UFczbThDUlR6WXhhYU1KeWJoL3JubXB1N2pZRUt6VWZkemgxTnZkMFZOVWhKQ1BGcjRhdlBoT2xxNjUiLCJtYWMiOiI2MjA1ZjU4YjhiMjRkMWNmNzJhYTNkMjJjNGRiYWEzNWUyOWY5ZDlhODJjMjZiMzBhOGRjNjJiZDhkZjc2NDY5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QZTB9Q66Z6TZdPmWFr3VIl4w177WleBhzBYndcG6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2138803094 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlkvT3NobFZPNU04dXhYNUdHUEYwUHc9PSIsInZhbHVlIjoidUNYR2VKV3NSUzJsSlYvSDJWOXZ2NVpVcjBsellkZmxHVVZmVHFYMERtVjg3YldFUzhVb29yVmJLdkszWFRlUGY4Yll0UFczbThDUlR6WXhhYU1KeWJoL3JubXB1N2pZRUt6VWZkemgxTnZkMFZOVWhKQ1BGcjRhdlBoT2xxNjUiLCJtYWMiOiI2MjA1ZjU4YjhiMjRkMWNmNzJhYTNkMjJjNGRiYWEzNWUyOWY5ZDlhODJjMjZiMzBhOGRjNjJiZDhkZjc2NDY5IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlNGOU5NZ2gxL1FWUkZGc2EzRkdkbUE9PSIsInZhbHVlIjoiSVRuT0FPazRVdFFBNHJqbFM4Yjh0M2xMbWhIYWJrV0ViRnZvNFppdnBpcUh3QVRMdWZlTm50Vk9tRXdmYmJkbGhJSlpCaGpSOUt5YWt0eHVteUJWYU5FNEI2c2JDTm01Vk9pNzFrbmRlTzlUL2d3aTdKVHFxL1JwMnB0V091RG8iLCJtYWMiOiJkNjRmY2E3M2NmYWMwMTJjZDRhOTI2MGIyNTNkODc1OTRlOGMxMzIyYjlhZWIwZWM0YjYyM2EzYjZlOTkxMjM0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138803094\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-224826609 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 19:02:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224826609\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-817977634 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-817977634\", {\"maxDepth\":0})</script>\n"}}