{"__meta": {"id": "Xce2cf13d7d0b09e05ee732460edfff45", "datetime": "2024-11-28 11:33:48", "utime": 1732804428.1617, "method": "GET", "uri": "/api/settings/banners", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804413.609617, "end": 1732804428.161727, "duration": 14.552109956741333, "duration_str": "14.55s", "measures": [{"label": "Booting", "start": 1732804413.609617, "relative_start": 0, "end": 1732804418.847033, "relative_end": 1732804418.847033, "duration": 5.23741602897644, "duration_str": "5.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732804418.84887, "relative_start": 5.239253044128418, "end": 1732804428.16173, "relative_end": 3.0994415283203125e-06, "duration": 9.312860012054443, "duration_str": "9.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14190136, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/settings/banners", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Settings\\BannerController@index", "namespace": null, "prefix": "api/settings/banners", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FSettings%2FBannerController.php&line=14\" onclick=\"\">app/Http/Controllers/Api/Settings/BannerController.php:14-17</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.14115, "accumulated_duration_str": "141ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `banners`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Settings/BannerController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Settings/BannerController.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.871884, "duration": 0.14115, "duration_str": "141ms", "memory": 0, "memory_str": null, "filename": "BannerController.php:16", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Settings/BannerController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Settings/BannerController.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FSettings%2FBannerController.php&line=16", "ajax": false, "filename": "BannerController.php", "line": "16"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Banner": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FBanner.php&line=1", "ajax": false, "filename": "Banner.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/settings/banners", "status_code": "<pre class=sf-dump id=sf-dump-206260897 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-206260897\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1891428816 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1891428816\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-289306729 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-289306729\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-775301539 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IjJUc2wrMmhzWmZVUlA1TjlvVzBOUWc9PSIsInZhbHVlIjoid3BjQXQwdWY3S3FaUDZ2M3Y5ajJ1TGtibVYvSWNLa3VncURlNUN2WGFQaFJ6Zk5Bc2U2allOVmhac1pEUXNwQ3pGVmhPZFhsbWdHc2p1VUFHT2d6eFFGczFFcjF3NzlFUTQvK0RKTXQ5ZjdOUWFEOUxSQ0VQa0Ewc2xVTk9oTkoiLCJtYWMiOiI2NWZkNDgxYjg0ZTg1N2ZjZmE0MjA2ODg2NjZmYmZhMDc1ZDhmNjRjMjk4ZDkwYmUxMzJjYzY2ODVjODczMDliIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6InBmQkh0U1JkVkgxdjA3cFN0OGdXRkE9PSIsInZhbHVlIjoiN2dnZm1Pd1h1NlRKMkg3Rk9QaFFYdzFSU09ZQUF4WDZ3U1JGUXRQRnVvUjRtSlgrTU91UmZJTCsxZmQ3WkZjbjBXbk02d3UzZVgxUEZxYXVNalpaRVVWMDdiWGI1UDFwaDRxcTdUWkdEY3J1cjhnUzU4Sm5PaU9WdHpkdFgralQiLCJtYWMiOiJiYWNmMzhlMGMxOGE0NmJjZDAyZWMwZGExYjQ0MTZhMmU0OTgwNTI5NmE3NzE2N2Y1Y2JlYWIyMmE2YzY1NjE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjJUc2wrMmhzWmZVUlA1TjlvVzBOUWc9PSIsInZhbHVlIjoid3BjQXQwdWY3S3FaUDZ2M3Y5ajJ1TGtibVYvSWNLa3VncURlNUN2WGFQaFJ6Zk5Bc2U2allOVmhac1pEUXNwQ3pGVmhPZFhsbWdHc2p1VUFHT2d6eFFGczFFcjF3NzlFUTQvK0RKTXQ5ZjdOUWFEOUxSQ0VQa0Ewc2xVTk9oTkoiLCJtYWMiOiI2NWZkNDgxYjg0ZTg1N2ZjZmE0MjA2ODg2NjZmYmZhMDc1ZDhmNjRjMjk4ZDkwYmUxMzJjYzY2ODVjODczMDliIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZxogPn2IBCCd0wbASYCv5voRQyu5lNXDFz0pAZq2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775301539\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-262023459 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjJUc2wrMmhzWmZVUlA1TjlvVzBOUWc9PSIsInZhbHVlIjoid3BjQXQwdWY3S3FaUDZ2M3Y5ajJ1TGtibVYvSWNLa3VncURlNUN2WGFQaFJ6Zk5Bc2U2allOVmhac1pEUXNwQ3pGVmhPZFhsbWdHc2p1VUFHT2d6eFFGczFFcjF3NzlFUTQvK0RKTXQ5ZjdOUWFEOUxSQ0VQa0Ewc2xVTk9oTkoiLCJtYWMiOiI2NWZkNDgxYjg0ZTg1N2ZjZmE0MjA2ODg2NjZmYmZhMDc1ZDhmNjRjMjk4ZDkwYmUxMzJjYzY2ODVjODczMDliIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InBmQkh0U1JkVkgxdjA3cFN0OGdXRkE9PSIsInZhbHVlIjoiN2dnZm1Pd1h1NlRKMkg3Rk9QaFFYdzFSU09ZQUF4WDZ3U1JGUXRQRnVvUjRtSlgrTU91UmZJTCsxZmQ3WkZjbjBXbk02d3UzZVgxUEZxYXVNalpaRVVWMDdiWGI1UDFwaDRxcTdUWkdEY3J1cjhnUzU4Sm5PaU9WdHpkdFgralQiLCJtYWMiOiJiYWNmMzhlMGMxOGE0NmJjZDAyZWMwZGExYjQ0MTZhMmU0OTgwNTI5NmE3NzE2N2Y1Y2JlYWIyMmE2YzY1NjE4IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-262023459\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1970547257 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:33:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970547257\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-651334549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-651334549\", {\"maxDepth\":0})</script>\n"}}