{"__meta": {"id": "Xfb702c9207568e18ccaacedff00e7d87", "datetime": "2024-11-28 12:15:57", "utime": 1732806957.528082, "method": "GET", "uri": "/api/casinos/games?category=pragmatic", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732806946.322124, "end": 1732806957.528108, "duration": 11.205983877182007, "duration_str": "11.21s", "measures": [{"label": "Booting", "start": 1732806946.322124, "relative_start": 0, "end": **********.235196, "relative_end": **********.235196, "duration": 2.913072109222412, "duration_str": "2.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.235262, "relative_start": 2.913137912750244, "end": 1732806957.52811, "relative_end": 2.1457672119140625e-06, "duration": 8.292848110198975, "duration_str": "8.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14453144, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/casinos/games", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@allGames", "namespace": null, "prefix": "api/casinos", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=388\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:388-414</a>"}, "queries": {"nb_statements": 20, "nb_visible_statements": 20, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.15710000000000002, "accumulated_duration_str": "157ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'pragmatic') and `status` = 1", "type": "query", "params": [], "bindings": ["pragmatic", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.3831902, "duration": 0.015560000000000001, "duration_str": "15.56ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 9.905}, {"sql": "select * from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'pragmatic') and `status` = 1 order by `views` desc limit 30 offset 0", "type": "query", "params": [], "bindings": ["pragmatic", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.491414, "duration": 0.00766, "duration_str": "7.66ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 9.905, "width_percent": 4.876}, {"sql": "select * from `providers` where `providers`.`id` in (15, 16, 18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.598677, "duration": 0.011609999999999999, "duration_str": "11.61ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 14.78, "width_percent": 7.39}, {"sql": "select `categories`.*, `category_game`.`game_id` as `pivot_game_id`, `category_game`.`category_id` as `pivot_category_id` from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `category_game`.`game_id` in (972, 987, 1028, 1031, 1164, 1167, 1170, 1171)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.6936622, "duration": 0.006059999999999999, "duration_str": "6.06ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 22.171, "width_percent": 3.857}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1167 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1167], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806956.422622, "duration": 0.01165, "duration_str": "11.65ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 26.028, "width_percent": 7.416}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1167 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1167], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806956.487723, "duration": 0.01472, "duration_str": "14.72ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 33.444, "width_percent": 9.37}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 972 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [972], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806956.572204, "duration": 0.00558, "duration_str": "5.58ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 42.813, "width_percent": 3.552}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 972 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [972], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806956.609048, "duration": 0.016739999999999998, "duration_str": "16.74ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 46.365, "width_percent": 10.656}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 987 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [987], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806956.701647, "duration": 0.00549, "duration_str": "5.49ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 57.021, "width_percent": 3.495}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 987 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [987], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806956.7518659, "duration": 0.01391, "duration_str": "13.91ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 60.516, "width_percent": 8.854}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1164 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1164], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806956.8257742, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 69.37, "width_percent": 1.528}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1164 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1164], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806956.8865888, "duration": 0.00692, "duration_str": "6.92ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 70.898, "width_percent": 4.405}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1170 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1170], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806956.96258, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 75.302, "width_percent": 0.49}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1170 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1170], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806957.033799, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 75.792, "width_percent": 1.241}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1171 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1171], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806957.104397, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 77.034, "width_percent": 0.859}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1171 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1171], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806957.162815, "duration": 0.0069900000000000006, "duration_str": "6.99ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 77.893, "width_percent": 4.449}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1028 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1028], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806957.230735, "duration": 0.01103, "duration_str": "11.03ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 82.342, "width_percent": 7.021}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1028 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1028], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806957.290637, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 89.363, "width_percent": 2.33}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1031 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1031], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806957.352166, "duration": 0.0056, "duration_str": "5.6ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 91.693, "width_percent": 3.565}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1031 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1031], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732806957.4020889, "duration": 0.00745, "duration_str": "7.45ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 95.258, "width_percent": 4.742}]}, "models": {"data": {"App\\Models\\Category": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Game": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=1", "ajax": false, "filename": "Game.php", "line": "?"}}, "App\\Models\\Provider": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FProvider.php&line=1", "ajax": false, "filename": "Provider.php", "line": "?"}}}, "count": 33, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/casinos/games", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"9 characters\">pragmatic</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-644103423 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IldZcFFBNkF0WXAwTlZUUkF0ZFRTS1E9PSIsInZhbHVlIjoidFF3QkhCSzdJTXlxYkhwZTkyWGc1MlNZSDljRyt1QnlQYnJicmJZSi8zWTZKSGt0Uk1VZmpreDdpYTFuU3ZXMjVjMzJBRFhEU1JnZEJkd3JGMTBpb3dvMWUxNkx2c0tFTUhSQUVKL1drcUV2RTlNZTNsL2hBUjZlMUU0UXBWb2YiLCJtYWMiOiJmZDE4Mzc1OTdlMzc1ODdlMGNkYzdmZTA5YTViYzE2ZmY0ZjZiZDc3NjVmYTE3MGIxOTQ5OTk0YzZmOTJjMGY1IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6IkNHRjMzcUF1bFhNOFUyM3gycVlTOVE9PSIsInZhbHVlIjoiRC9mNiszTHd3MjMrU3c4dG4xUGE5QTNhTXVOeng1NWV1YW5jaWs5TThiZGhScVlOcWI4cmZaNms3ZHdsemdYTjNiaitMeWNMVHErcXBocEFTaVZQQzdmOXd1dTdXRFVISXdYWlBsSkhxTWttbjVLaGZ4dnhVVDZyMmRTQmRwVEgiLCJtYWMiOiJjZjlkOWMzMmRmMDgwZjY3YWYxNTRhYTAwMWI0YmYzNDg1ODg5YjliYWM3Y2E0ZmY2NjUzNWE1ZDQxODNlYmIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IldZcFFBNkF0WXAwTlZUUkF0ZFRTS1E9PSIsInZhbHVlIjoidFF3QkhCSzdJTXlxYkhwZTkyWGc1MlNZSDljRyt1QnlQYnJicmJZSi8zWTZKSGt0Uk1VZmpreDdpYTFuU3ZXMjVjMzJBRFhEU1JnZEJkd3JGMTBpb3dvMWUxNkx2c0tFTUhSQUVKL1drcUV2RTlNZTNsL2hBUjZlMUU0UXBWb2YiLCJtYWMiOiJmZDE4Mzc1OTdlMzc1ODdlMGNkYzdmZTA5YTViYzE2ZmY0ZjZiZDc3NjVmYTE3MGIxOTQ5OTk0YzZmOTJjMGY1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZE0YVLZQjR9JyraSrLfdfqjJhqLh1JxweQcNpzyV</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644103423\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-826698756 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IldZcFFBNkF0WXAwTlZUUkF0ZFRTS1E9PSIsInZhbHVlIjoidFF3QkhCSzdJTXlxYkhwZTkyWGc1MlNZSDljRyt1QnlQYnJicmJZSi8zWTZKSGt0Uk1VZmpreDdpYTFuU3ZXMjVjMzJBRFhEU1JnZEJkd3JGMTBpb3dvMWUxNkx2c0tFTUhSQUVKL1drcUV2RTlNZTNsL2hBUjZlMUU0UXBWb2YiLCJtYWMiOiJmZDE4Mzc1OTdlMzc1ODdlMGNkYzdmZTA5YTViYzE2ZmY0ZjZiZDc3NjVmYTE3MGIxOTQ5OTk0YzZmOTJjMGY1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkNHRjMzcUF1bFhNOFUyM3gycVlTOVE9PSIsInZhbHVlIjoiRC9mNiszTHd3MjMrU3c4dG4xUGE5QTNhTXVOeng1NWV1YW5jaWs5TThiZGhScVlOcWI4cmZaNms3ZHdsemdYTjNiaitMeWNMVHErcXBocEFTaVZQQzdmOXd1dTdXRFVISXdYWlBsSkhxTWttbjVLaGZ4dnhVVDZyMmRTQmRwVEgiLCJtYWMiOiJjZjlkOWMzMmRmMDgwZjY3YWYxNTRhYTAwMWI0YmYzNDg1ODg5YjliYWM3Y2E0ZmY2NjUzNWE1ZDQxODNlYmIwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-826698756\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-864369587 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 15:15:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-864369587\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-209505977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-209505977\", {\"maxDepth\":0})</script>\n"}}