{"__meta": {"id": "Xf2f9c4bc9a13752f9f7ad27403c567c2", "datetime": "2024-11-28 12:38:32", "utime": 1732808312.999705, "method": "GET", "uri": "/api/casinos/games?category=pragmatic", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732808298.468715, "end": 1732808312.999725, "duration": 14.531010150909424, "duration_str": "14.53s", "measures": [{"label": "Booting", "start": 1732808298.468715, "relative_start": 0, "end": **********.695369, "relative_end": **********.695369, "duration": 4.226654052734375, "duration_str": "4.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.695385, "relative_start": 4.226670026779175, "end": 1732808312.999728, "relative_end": 2.86102294921875e-06, "duration": 10.304342985153198, "duration_str": "10.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14467624, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/casinos/games", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@allGames", "namespace": null, "prefix": "api/casinos", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=388\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:388-414</a>"}, "queries": {"nb_statements": 36, "nb_visible_statements": 36, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.35704, "accumulated_duration_str": "357ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'pragmatic') and `status` = 1", "type": "query", "params": [], "bindings": ["pragmatic", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.8950658, "duration": 0.01219, "duration_str": "12.19ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 3.414}, {"sql": "select * from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'pragmatic') and `status` = 1 order by `views` desc limit 30 offset 0", "type": "query", "params": [], "bindings": ["pragmatic", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.035011, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 3.414, "width_percent": 1.011}, {"sql": "select * from `providers` where `providers`.`id` in (15, 16, 18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.169922, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 4.425, "width_percent": 0.473}, {"sql": "select `categories`.*, `category_game`.`game_id` as `pivot_game_id`, `category_game`.`category_id` as `pivot_category_id` from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `category_game`.`game_id` in (972, 987, 1028, 1031, 1164, 1167, 1170, 1171)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.2653751, "duration": 0.00818, "duration_str": "8.18ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 4.899, "width_percent": 2.291}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1167 limit 1", "type": "query", "params": [], "bindings": [********, 1167], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808310.72735, "duration": 0.00615, "duration_str": "6.15ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 7.19, "width_percent": 1.722}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1167 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1167], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808310.8034642, "duration": 0.02081, "duration_str": "20.81ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 8.912, "width_percent": 5.828}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1167 limit 1", "type": "query", "params": [], "bindings": [********, 1167], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808310.868186, "duration": 0.0238, "duration_str": "23.8ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 14.741, "width_percent": 6.666}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1167 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1167], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808310.9590719, "duration": 0.01882, "duration_str": "18.82ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 21.407, "width_percent": 5.271}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 972 limit 1", "type": "query", "params": [], "bindings": [********, 972], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.0863101, "duration": 0.00941, "duration_str": "9.41ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 26.678, "width_percent": 2.636}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 972 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [972], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.1670718, "duration": 0.01483, "duration_str": "14.83ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 29.313, "width_percent": 4.154}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 972 limit 1", "type": "query", "params": [], "bindings": [********, 972], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.230098, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 33.467, "width_percent": 0.202}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 972 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [972], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.290953, "duration": 0.014960000000000001, "duration_str": "14.96ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 33.668, "width_percent": 4.19}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 987 limit 1", "type": "query", "params": [], "bindings": [********, 987], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.366682, "duration": 0.01486, "duration_str": "14.86ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 37.859, "width_percent": 4.162}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 987 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [987], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.44185, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 42.021, "width_percent": 0.112}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 987 limit 1", "type": "query", "params": [], "bindings": [********, 987], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.500581, "duration": 0.00877, "duration_str": "8.77ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 42.133, "width_percent": 2.456}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 987 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [987], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.56758, "duration": 0.01282, "duration_str": "12.82ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 44.589, "width_percent": 3.591}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1164 limit 1", "type": "query", "params": [], "bindings": [********, 1164], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.6504161, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 48.179, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1164 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1164], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.700077, "duration": 0.02126, "duration_str": "21.26ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 48.317, "width_percent": 5.955}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1164 limit 1", "type": "query", "params": [], "bindings": [********, 1164], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.756166, "duration": 0.01995, "duration_str": "19.95ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 54.271, "width_percent": 5.588}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1164 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1164], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.84372, "duration": 0.006019999999999999, "duration_str": "6.02ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 59.859, "width_percent": 1.686}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1170 limit 1", "type": "query", "params": [], "bindings": [********, 1170], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.907324, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 61.545, "width_percent": 0.678}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1170 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1170], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808311.9783661, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 62.223, "width_percent": 0.91}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1170 limit 1", "type": "query", "params": [], "bindings": [********, 1170], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.041942, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 63.133, "width_percent": 0.353}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1170 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1170], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.095168, "duration": 0.009089999999999999, "duration_str": "9.09ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 63.486, "width_percent": 2.546}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1171 limit 1", "type": "query", "params": [], "bindings": [********, 1171], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.152499, "duration": 0.014070000000000001, "duration_str": "14.07ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 66.032, "width_percent": 3.941}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1171 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1171], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.233732, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 69.973, "width_percent": 0.148}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1171 limit 1", "type": "query", "params": [], "bindings": [********, 1171], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.265097, "duration": 0.028829999999999998, "duration_str": "28.83ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 70.121, "width_percent": 8.075}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1171 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1171], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.357369, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 78.196, "width_percent": 1.252}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1028 limit 1", "type": "query", "params": [], "bindings": [********, 1028], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.433593, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 79.448, "width_percent": 0.224}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1028 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1028], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.4903622, "duration": 0.00731, "duration_str": "7.31ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 79.672, "width_percent": 2.047}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1028 limit 1", "type": "query", "params": [], "bindings": [********, 1028], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.557966, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 81.719, "width_percent": 0.126}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1028 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1028], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.6301801, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 81.845, "width_percent": 0.933}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1031 limit 1", "type": "query", "params": [], "bindings": [********, 1031], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.686588, "duration": 0.01491, "duration_str": "14.91ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 82.778, "width_percent": 4.176}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1031 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1031], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.766916, "duration": 0.011, "duration_str": "11ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 86.954, "width_percent": 3.081}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1031 limit 1", "type": "query", "params": [], "bindings": [********, 1031], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.826601, "duration": 0.01092, "duration_str": "10.92ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 90.035, "width_percent": 3.058}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1031 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1031], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732808312.8768198, "duration": 0.02466, "duration_str": "24.66ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 93.093, "width_percent": 6.907}]}, "models": {"data": {"App\\Models\\Category": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Game": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=1", "ajax": false, "filename": "Game.php", "line": "?"}}, "App\\Models\\Provider": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FProvider.php&line=1", "ajax": false, "filename": "Provider.php", "line": "?"}}}, "count": 33, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/casinos/games", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"9 characters\">pragmatic</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1733921625 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IjRhS0NRVzV6WEljTUlrUTl1SmNIaVE9PSIsInZhbHVlIjoidkExR2ZVcnNJZGw3QVZ6MU1iY2pRcHlTVFlNNW9xbW51L1pEb0hMWEZSdlI5YlpUMGtvQXJ2ZW9vTWp5aU8yOE9nWE0vMW1GaVpxaEpLZWhpd2lQQ3RwWStQYmc3aEVnc3VjSjJaSU5Uc1Y1aldyN09MdzlKRXJRSmVWN1kxcnciLCJtYWMiOiIwN2M1NjJhYjhhMzM3NmM3MWIyOTU1MzRhODFlY2MxMjFiNjE0YjliZWVlYzJjMTViNTc2ZmU5YmM0YTMwYTcxIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6Im5ndXltUFVBcnNBZjRyS1hvdEREUWc9PSIsInZhbHVlIjoiV1g0VWo5ZFQwQW5Xc215b294TzlYRGhZV0toMjdDZ3VrUkQrMS84ZWYwVno1T2hqQ3Z4RXk0TUkzeWo2QitKR0doaWhhNHZ6TW02aE5sYVRlNDdPOG1VdGhNbFFuU0UySlVtWUtIUC9IVHlHOTVKRXluenlNYU8rQXFhWW1pSnUiLCJtYWMiOiIwYTI1ZTMyZDRmZmU3YWVlNGUwN2I2ZmIyZjVjMmIxZDg5NDA3MzQxYTMxZDAyOTZjMjc2N2Y0NzMwMjg3NThkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRhS0NRVzV6WEljTUlrUTl1SmNIaVE9PSIsInZhbHVlIjoidkExR2ZVcnNJZGw3QVZ6MU1iY2pRcHlTVFlNNW9xbW51L1pEb0hMWEZSdlI5YlpUMGtvQXJ2ZW9vTWp5aU8yOE9nWE0vMW1GaVpxaEpLZWhpd2lQQ3RwWStQYmc3aEVnc3VjSjJaSU5Uc1Y1aldyN09MdzlKRXJRSmVWN1kxcnciLCJtYWMiOiIwN2M1NjJhYjhhMzM3NmM3MWIyOTU1MzRhODFlY2MxMjFiNjE0YjliZWVlYzJjMTViNTc2ZmU5YmM0YTMwYTcxIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZE0YVLZQjR9JyraSrLfdfqjJhqLh1JxweQcNpzyV</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733921625\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-147929866 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRhS0NRVzV6WEljTUlrUTl1SmNIaVE9PSIsInZhbHVlIjoidkExR2ZVcnNJZGw3QVZ6MU1iY2pRcHlTVFlNNW9xbW51L1pEb0hMWEZSdlI5YlpUMGtvQXJ2ZW9vTWp5aU8yOE9nWE0vMW1GaVpxaEpLZWhpd2lQQ3RwWStQYmc3aEVnc3VjSjJaSU5Uc1Y1aldyN09MdzlKRXJRSmVWN1kxcnciLCJtYWMiOiIwN2M1NjJhYjhhMzM3NmM3MWIyOTU1MzRhODFlY2MxMjFiNjE0YjliZWVlYzJjMTViNTc2ZmU5YmM0YTMwYTcxIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im5ndXltUFVBcnNBZjRyS1hvdEREUWc9PSIsInZhbHVlIjoiV1g0VWo5ZFQwQW5Xc215b294TzlYRGhZV0toMjdDZ3VrUkQrMS84ZWYwVno1T2hqQ3Z4RXk0TUkzeWo2QitKR0doaWhhNHZ6TW02aE5sYVRlNDdPOG1VdGhNbFFuU0UySlVtWUtIUC9IVHlHOTVKRXluenlNYU8rQXFhWW1pSnUiLCJtYWMiOiIwYTI1ZTMyZDRmZmU3YWVlNGUwN2I2ZmIyZjVjMmIxZDg5NDA3MzQxYTMxZDAyOTZjMjc2N2Y0NzMwMjg3NThkIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147929866\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1436980542 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 15:38:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436980542\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1051674726 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1051674726\", {\"maxDepth\":0})</script>\n"}}