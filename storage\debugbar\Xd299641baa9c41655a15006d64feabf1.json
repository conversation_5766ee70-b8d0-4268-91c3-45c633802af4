{"__meta": {"id": "Xd299641baa9c41655a15006d64feabf1", "datetime": "2024-11-28 13:03:52", "utime": **********.708427, "method": "GET", "uri": "/api/settings/banners", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732809828.106454, "end": **********.708449, "duration": 4.60199499130249, "duration_str": "4.6s", "measures": [{"label": "Booting", "start": 1732809828.106454, "relative_start": 0, "end": 1732809829.263318, "relative_end": 1732809829.263318, "duration": 1.1568641662597656, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732809829.263346, "relative_start": 1.1568920612335205, "end": **********.708452, "relative_end": 3.0994415283203125e-06, "duration": 3.445106029510498, "duration_str": "3.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14190136, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/settings/banners", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Settings\\BannerController@index", "namespace": null, "prefix": "api/settings/banners", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FSettings%2FBannerController.php&line=14\" onclick=\"\">app/Http/Controllers/Api/Settings/BannerController.php:14-17</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01884, "accumulated_duration_str": "18.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `banners`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Settings/BannerController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Settings/BannerController.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.64886, "duration": 0.01884, "duration_str": "18.84ms", "memory": 0, "memory_str": null, "filename": "BannerController.php:16", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Settings/BannerController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Settings/BannerController.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FSettings%2FBannerController.php&line=16", "ajax": false, "filename": "BannerController.php", "line": "16"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Banner": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FBanner.php&line=1", "ajax": false, "filename": "Banner.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/settings/banners", "status_code": "<pre class=sf-dump id=sf-dump-1347036873 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1347036873\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1362598937 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1362598937\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-775632295 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-775632295\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2106680690 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6InlhdENmb2xORDZjbkNWMkdWSkVmZnc9PSIsInZhbHVlIjoia3A2b2Y3MFZmNDlDNGR3MWwybGtHUUFVbUVwb24zKzBQRGlhZnhUd1oxUmxNVHNWNVRxU21qdSs5Qk1VZ3ZnRlZKVWRCSGxDd2lDRUJRMXl2bzlsTzM2dUp3YytJVk8vVzJ4OGpMS2xtRElOU1hudFQ1RzdKNGlyVzF1dEFvNTEiLCJtYWMiOiJjOTYzMmM5YmU2OTk0NjMwZjZhNjZiMjc5ZGJjZGE2YTEwNjk1OTUxODFiMmRjZTIyMTI5MTcwY2U2ZmMyZDQ0IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6InI2dEN0aWNDbHhudEpFY1E5cFA0WWc9PSIsInZhbHVlIjoiR05HT055cTR6TUJpRzgveU9HcTJ1T0JQV29kSkJwQUhQeTluRmJDVGlFWTBRQUlOUHFpellGckNYTmh4SEVYdEdsRG9iZUpuMXdoRTdhT2h1SU1hWGJqbWZFN1AzeWRXR202TEx3elR5L0hIcGJ0MWNoVnpYWU5jVDJLWlpjL0kiLCJtYWMiOiJmMDY4NjVkOTFiNWZkYTA3YzcyOTBmMzAzZjlkZGUyZmY1ZmNmOWRjMWM5NjIzN2JmN2RlZDBkN2IzNjQ4NDBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"133 characters\">Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InlhdENmb2xORDZjbkNWMkdWSkVmZnc9PSIsInZhbHVlIjoia3A2b2Y3MFZmNDlDNGR3MWwybGtHUUFVbUVwb24zKzBQRGlhZnhUd1oxUmxNVHNWNVRxU21qdSs5Qk1VZ3ZnRlZKVWRCSGxDd2lDRUJRMXl2bzlsTzM2dUp3YytJVk8vVzJ4OGpMS2xtRElOU1hudFQ1RzdKNGlyVzF1dEFvNTEiLCJtYWMiOiJjOTYzMmM5YmU2OTk0NjMwZjZhNjZiMjc5ZGJjZGE2YTEwNjk1OTUxODFiMmRjZTIyMTI5MTcwY2U2ZmMyZDQ0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QZTB9Q66Z6TZdPmWFr3VIl4w177WleBhzBYndcG6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106680690\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-774226939 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InlhdENmb2xORDZjbkNWMkdWSkVmZnc9PSIsInZhbHVlIjoia3A2b2Y3MFZmNDlDNGR3MWwybGtHUUFVbUVwb24zKzBQRGlhZnhUd1oxUmxNVHNWNVRxU21qdSs5Qk1VZ3ZnRlZKVWRCSGxDd2lDRUJRMXl2bzlsTzM2dUp3YytJVk8vVzJ4OGpMS2xtRElOU1hudFQ1RzdKNGlyVzF1dEFvNTEiLCJtYWMiOiJjOTYzMmM5YmU2OTk0NjMwZjZhNjZiMjc5ZGJjZGE2YTEwNjk1OTUxODFiMmRjZTIyMTI5MTcwY2U2ZmMyZDQ0IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InI2dEN0aWNDbHhudEpFY1E5cFA0WWc9PSIsInZhbHVlIjoiR05HT055cTR6TUJpRzgveU9HcTJ1T0JQV29kSkJwQUhQeTluRmJDVGlFWTBRQUlOUHFpellGckNYTmh4SEVYdEdsRG9iZUpuMXdoRTdhT2h1SU1hWGJqbWZFN1AzeWRXR202TEx3elR5L0hIcGJ0MWNoVnpYWU5jVDJLWlpjL0kiLCJtYWMiOiJmMDY4NjVkOTFiNWZkYTA3YzcyOTBmMzAzZjlkZGUyZmY1ZmNmOWRjMWM5NjIzN2JmN2RlZDBkN2IzNjQ4NDBiIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-774226939\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-127043564 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 16:03:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-127043564\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-970521765 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-970521765\", {\"maxDepth\":0})</script>\n"}}