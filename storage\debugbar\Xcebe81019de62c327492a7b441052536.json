{"__meta": {"id": "Xcebe81019de62c327492a7b441052536", "datetime": "2024-11-28 11:33:31", "utime": 1732804411.977515, "method": "GET", "uri": "/home/<USER>", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804407.786951, "end": 1732804411.97756, "duration": 4.190608978271484, "duration_str": "4.19s", "measures": [{"label": "Booting", "start": 1732804407.786951, "relative_start": 0, "end": 1732804408.836727, "relative_end": 1732804408.836727, "duration": 1.0497758388519287, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732804408.837531, "relative_start": 1.0505800247192383, "end": 1732804411.977564, "relative_end": 4.0531158447265625e-06, "duration": 3.140033006668091, "duration_str": "3.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14248184, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "layouts.app", "param_count": null, "params": [], "start": 1732804411.658927, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/resources/views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "route": {"uri": "GET {view}", "middleware": "web", "uses": "App\\Http\\Controllers\\Layouts\\ApplicationController@__invoke", "controller": "App\\Http\\Controllers\\Layouts\\ApplicationController", "namespace": null, "prefix": "", "where": []}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.13954, "accumulated_duration_str": "140ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `software_name`, `software_description`, `software_logo_white`, `software_logo_black`, `currency_code`, `decimal_format`, `currency_position`, `prefix`, `storage`, `min_deposit`, `max_deposit`, `min_withdrawal`, `max_withdrawal`, `initial_bonus`, `suitpay_is_enable`, `bspay_is_enable`, `stripe_is_enable`, `sharkpay_is_enable`, `ezzebank_is_enable`, `disable_spin`, `disable_rollover` from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 998}, {"index": 17, "namespace": "view", "name": "layouts.app", "file": "/home/<USER>/htdocs/job.forradapg.com/resources/views/layouts/app.blade.php", "line": 14}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 22}], "start": 1732804411.702146, "duration": 0.12478, "duration_str": "125ms", "memory": 0, "memory_str": null, "filename": "Core.php:998", "source": {"index": 16, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 998}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHelpers%2FCore.php&line=998", "ajax": false, "filename": "Core.php", "line": "998"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 89.422}, {"sql": "select * from `custom_layouts` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 959}, {"index": 20, "namespace": "view", "name": "layouts.app", "file": "/home/<USER>/htdocs/job.forradapg.com/resources/views/layouts/app.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 22}], "start": 1732804411.894453, "duration": 0.01476, "duration_str": "14.76ms", "memory": 0, "memory_str": null, "filename": "Core.php:959", "source": {"index": 19, "namespace": null, "name": "app/Helpers/Core.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Helpers/Core.php", "line": 959}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHelpers%2FCore.php&line=959", "ajax": false, "filename": "Core.php", "line": "959"}, "connection": "china15tema2", "explain": null, "start_percent": 89.422, "width_percent": 10.578}]}, "models": {"data": {"App\\Models\\Setting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\CustomLayout": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FCustomLayout.php&line=1", "ajax": false, "filename": "CustomLayout.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ZxogPn2IBCCd0wbASYCv5voRQyu5lNXDFz0pAZq2", "_previous": "array:1 [\n  \"url\" => \"https://job.forradapg.com/home/<USER>\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/home/<USER>", "status_code": "<pre class=sf-dump id=sf-dump-2053940415 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2053940415\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1906197091 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>gameCategoryId</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906197091\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1239263347 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1239263347\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1997797980 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6Im9GVlhWUWhIR0luVnhEVEFlRzIzamc9PSIsInZhbHVlIjoiNnM4N0RBVnFCeFdPbFFTOXlWZW5aOXN1ODBxTnduYUF3WU9yQitaK2M1MERFT0pHc2NCREtvcE9VVCtVN2krNWh2STBGbEhraTNkdnI2TUxHR05McTVWb1I0QlpPcDJPWjhCeWpsZHNVcVNxRG1IS3crQ2VqSTFCY2kxWVNORUkiLCJtYWMiOiJmOWJmMjk5ZTBjMTA2MDc2ZTA1ZWRkMTNlYmM4NzJlNDA5ZDQxY2M5ODljMzQ3ZDYzZGZmYmNiZGU0YWQ2NDNhIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6InZwcEdpdmh0RDFXcFIrdmFkWEk3SWc9PSIsInZhbHVlIjoicTRCZTVwRGo1a1BlMFZyUWxmRHBXaGRFbTBudHB2NFZLdlBLR05uWFdEQmhqODhwUWdFK2V2OFFLTlRzWTY1WmFXVGp6bjA5MEcwMVo5TVBUYzQ5SzZoemNIT1krczNTc09WN0tWaVQyYjZiZkZqeis4SGNDTUxDVm54dmVCYTciLCJtYWMiOiI2M2NjMWIxOTEyMjhjOGM0NzNjNGM0MzZkNDU2ODlhMzFkYmRjOWRjOWYyZWY2NDE1ZGJlNTgxM2E1Y2I2NDM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997797980\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-594919778 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxogPn2IBCCd0wbASYCv5voRQyu5lNXDFz0pAZq2</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3LPI7n3LMw97j3kYBennrzAGNxb2F6sUSq7CQtJA</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594919778\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-895184526 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:33:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJUc2wrMmhzWmZVUlA1TjlvVzBOUWc9PSIsInZhbHVlIjoid3BjQXQwdWY3S3FaUDZ2M3Y5ajJ1TGtibVYvSWNLa3VncURlNUN2WGFQaFJ6Zk5Bc2U2allOVmhac1pEUXNwQ3pGVmhPZFhsbWdHc2p1VUFHT2d6eFFGczFFcjF3NzlFUTQvK0RKTXQ5ZjdOUWFEOUxSQ0VQa0Ewc2xVTk9oTkoiLCJtYWMiOiI2NWZkNDgxYjg0ZTg1N2ZjZmE0MjA2ODg2NjZmYmZhMDc1ZDhmNjRjMjk4ZDkwYmUxMzJjYzY2ODVjODczMDliIiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 16:33:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6InBmQkh0U1JkVkgxdjA3cFN0OGdXRkE9PSIsInZhbHVlIjoiN2dnZm1Pd1h1NlRKMkg3Rk9QaFFYdzFSU09ZQUF4WDZ3U1JGUXRQRnVvUjRtSlgrTU91UmZJTCsxZmQ3WkZjbjBXbk02d3UzZVgxUEZxYXVNalpaRVVWMDdiWGI1UDFwaDRxcTdUWkdEY3J1cjhnUzU4Sm5PaU9WdHpkdFgralQiLCJtYWMiOiJiYWNmMzhlMGMxOGE0NmJjZDAyZWMwZGExYjQ0MTZhMmU0OTgwNTI5NmE3NzE2N2Y1Y2JlYWIyMmE2YzY1NjE4IiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 16:33:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJUc2wrMmhzWmZVUlA1TjlvVzBOUWc9PSIsInZhbHVlIjoid3BjQXQwdWY3S3FaUDZ2M3Y5ajJ1TGtibVYvSWNLa3VncURlNUN2WGFQaFJ6Zk5Bc2U2allOVmhac1pEUXNwQ3pGVmhPZFhsbWdHc2p1VUFHT2d6eFFGczFFcjF3NzlFUTQvK0RKTXQ5ZjdOUWFEOUxSQ0VQa0Ewc2xVTk9oTkoiLCJtYWMiOiI2NWZkNDgxYjg0ZTg1N2ZjZmE0MjA2ODg2NjZmYmZhMDc1ZDhmNjRjMjk4ZDkwYmUxMzJjYzY2ODVjODczMDliIiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 16:33:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6InBmQkh0U1JkVkgxdjA3cFN0OGdXRkE9PSIsInZhbHVlIjoiN2dnZm1Pd1h1NlRKMkg3Rk9QaFFYdzFSU09ZQUF4WDZ3U1JGUXRQRnVvUjRtSlgrTU91UmZJTCsxZmQ3WkZjbjBXbk02d3UzZVgxUEZxYXVNalpaRVVWMDdiWGI1UDFwaDRxcTdUWkdEY3J1cjhnUzU4Sm5PaU9WdHpkdFgralQiLCJtYWMiOiJiYWNmMzhlMGMxOGE0NmJjZDAyZWMwZGExYjQ0MTZhMmU0OTgwNTI5NmE3NzE2N2Y1Y2JlYWIyMmE2YzY1NjE4IiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 16:33:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895184526\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-943109062 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZxogPn2IBCCd0wbASYCv5voRQyu5lNXDFz0pAZq2</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943109062\", {\"maxDepth\":0})</script>\n"}}