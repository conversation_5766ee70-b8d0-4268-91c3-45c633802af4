{"__meta": {"id": "Xf169bbd95e95f834b832ee0764403094", "datetime": "2024-11-28 17:04:47", "utime": 1732824287.625694, "method": "GET", "uri": "/api/profile/wallet", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732824283.372771, "end": 1732824287.625714, "duration": 4.25294303894043, "duration_str": "4.25s", "measures": [{"label": "Booting", "start": 1732824283.372771, "relative_start": 0, "end": **********.58363, "relative_end": **********.58363, "duration": 1.2108590602874756, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.583647, "relative_start": 1.2108759880065918, "end": 1732824287.625716, "relative_end": 1.9073486328125e-06, "duration": 3.0420689582824707, "duration_str": "3.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14297256, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/profile/wallet", "middleware": "api, auth.jwt", "controller": "App\\Http\\Controllers\\Api\\Profile\\WalletController@index", "namespace": null, "prefix": "api/profile/wallet", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=19\" onclick=\"\">app/Http/Controllers/Api/Profile/WalletController.php:19-23</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00311, "accumulated_duration_str": "3.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `wallets` where `user_id` = ******** and `active` = 1 limit 1", "type": "query", "params": [], "bindings": [********, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.6432621, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "WalletController.php:21", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Profile/WalletController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Profile/WalletController.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FProfile%2FWalletController.php&line=21", "ajax": false, "filename": "WalletController.php", "line": "21"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Wallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/profile/wallet", "status_code": "<pre class=sf-dump id=sf-dump-683793340 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-683793340\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-115265988 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-115265988\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1150612563 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1150612563\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1903589079 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IkZvd093Zzk1NmpXbndlMzQvYW5Zenc9PSIsInZhbHVlIjoibTc1WmxtZ2ttM1hSZFkxMXRUV2NoMU5tb1k5a2o2RWhQRmZCekZ2RW5hc3AxQWJiSVB6Qm9hdzR3Z3VDa1NNR3pxeStGUm5GQU9rZ2k3dXQ3TVNSb25TUFUvQXcxcFhoNCtQMWJKc1ZabDFZbXAvSWgxZG9MM3ZiTlhMeVFFUloiLCJtYWMiOiI2MzExNjI2NzUyNTljMzVmZmI0MGUzY2FhNmM0NzIzZGRhYTVhZjNmYjU4ODJlNDFlZDdhNzdkMzU0MmFjOWM3IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6IlQvVHd1NG00aTdnL2N4LzhaTHFwL0E9PSIsInZhbHVlIjoiYWhXNXBQOWRSNFZDbmUxT282dVhodHlDSlFBd29hY05Lb3ZFdVFQa3V6cFRJTEFmNk1nQk9PU1VHYWVVOGtyZkh6Z2RIRk9hM2wxRnlGalpaVEEwdkNiSHhDdUdLaGUwSXlkWUFrbm16MnV4dEdpTXBxcXA1Y3hNTXVGQ21aN2ciLCJtYWMiOiJmOWZjODUxZWMxYmJmMzdjM2JmOTM0NTVhYWIwZWU1MDU4YWY4NjA4ZWE0OTlmNDYzZWNhZDRjYmYwMGFiNDcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"133 characters\">Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZvd093Zzk1NmpXbndlMzQvYW5Zenc9PSIsInZhbHVlIjoibTc1WmxtZ2ttM1hSZFkxMXRUV2NoMU5tb1k5a2o2RWhQRmZCekZ2RW5hc3AxQWJiSVB6Qm9hdzR3Z3VDa1NNR3pxeStGUm5GQU9rZ2k3dXQ3TVNSb25TUFUvQXcxcFhoNCtQMWJKc1ZabDFZbXAvSWgxZG9MM3ZiTlhMeVFFUloiLCJtYWMiOiI2MzExNjI2NzUyNTljMzVmZmI0MGUzY2FhNmM0NzIzZGRhYTVhZjNmYjU4ODJlNDFlZDdhNzdkMzU0MmFjOWM3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QZTB9Q66Z6TZdPmWFr3VIl4w177WleBhzBYndcG6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903589079\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2050864100 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZvd093Zzk1NmpXbndlMzQvYW5Zenc9PSIsInZhbHVlIjoibTc1WmxtZ2ttM1hSZFkxMXRUV2NoMU5tb1k5a2o2RWhQRmZCekZ2RW5hc3AxQWJiSVB6Qm9hdzR3Z3VDa1NNR3pxeStGUm5GQU9rZ2k3dXQ3TVNSb25TUFUvQXcxcFhoNCtQMWJKc1ZabDFZbXAvSWgxZG9MM3ZiTlhMeVFFUloiLCJtYWMiOiI2MzExNjI2NzUyNTljMzVmZmI0MGUzY2FhNmM0NzIzZGRhYTVhZjNmYjU4ODJlNDFlZDdhNzdkMzU0MmFjOWM3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlQvVHd1NG00aTdnL2N4LzhaTHFwL0E9PSIsInZhbHVlIjoiYWhXNXBQOWRSNFZDbmUxT282dVhodHlDSlFBd29hY05Lb3ZFdVFQa3V6cFRJTEFmNk1nQk9PU1VHYWVVOGtyZkh6Z2RIRk9hM2wxRnlGalpaVEEwdkNiSHhDdUdLaGUwSXlkWUFrbm16MnV4dEdpTXBxcXA1Y3hNTXVGQ21aN2ciLCJtYWMiOiJmOWZjODUxZWMxYmJmMzdjM2JmOTM0NTVhYWIwZWU1MDU4YWY4NjA4ZWE0OTlmNDYzZWNhZDRjYmYwMGFiNDcwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050864100\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1687825973 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 20:04:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687825973\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-787991319 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-787991319\", {\"maxDepth\":0})</script>\n"}}