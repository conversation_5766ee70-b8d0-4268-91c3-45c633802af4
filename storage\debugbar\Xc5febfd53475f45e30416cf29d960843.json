{"__meta": {"id": "Xc5febfd53475f45e30416cf29d960843", "datetime": "2024-11-28 12:38:04", "utime": 1732808284.057819, "method": "GET", "uri": "/api/categories", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732808273.333958, "end": 1732808284.057846, "duration": 10.723888158798218, "duration_str": "10.72s", "measures": [{"label": "Booting", "start": 1732808273.333958, "relative_start": 0, "end": 1732808276.945525, "relative_end": 1732808276.945525, "duration": 3.6115670204162598, "duration_str": "3.61s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732808276.94554, "relative_start": 3.611582040786743, "end": 1732808284.057848, "relative_end": 1.9073486328125e-06, "duration": 7.112308025360107, "duration_str": "7.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14320440, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/categories", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Categories\\CategoryController@index", "namespace": null, "prefix": "api/categories", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FCategories%2FCategoryController.php&line=14\" onclick=\"\">app/Http/Controllers/Api/Categories/CategoryController.php:14-18</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01849, "accumulated_duration_str": "18.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Categories/CategoryController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Categories/CategoryController.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.031277, "duration": 0.01849, "duration_str": "18.49ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:16", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/Categories/CategoryController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Categories/CategoryController.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FCategories%2FCategoryController.php&line=16", "ajax": false, "filename": "CategoryController.php", "line": "16"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Category": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}}, "count": 8, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/categories", "status_code": "<pre class=sf-dump id=sf-dump-2088232328 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2088232328\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-472339857 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-472339857\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-440446005 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-440446005\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-304561111 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IjRhS0NRVzV6WEljTUlrUTl1SmNIaVE9PSIsInZhbHVlIjoidkExR2ZVcnNJZGw3QVZ6MU1iY2pRcHlTVFlNNW9xbW51L1pEb0hMWEZSdlI5YlpUMGtvQXJ2ZW9vTWp5aU8yOE9nWE0vMW1GaVpxaEpLZWhpd2lQQ3RwWStQYmc3aEVnc3VjSjJaSU5Uc1Y1aldyN09MdzlKRXJRSmVWN1kxcnciLCJtYWMiOiIwN2M1NjJhYjhhMzM3NmM3MWIyOTU1MzRhODFlY2MxMjFiNjE0YjliZWVlYzJjMTViNTc2ZmU5YmM0YTMwYTcxIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6Im5ndXltUFVBcnNBZjRyS1hvdEREUWc9PSIsInZhbHVlIjoiV1g0VWo5ZFQwQW5Xc215b294TzlYRGhZV0toMjdDZ3VrUkQrMS84ZWYwVno1T2hqQ3Z4RXk0TUkzeWo2QitKR0doaWhhNHZ6TW02aE5sYVRlNDdPOG1VdGhNbFFuU0UySlVtWUtIUC9IVHlHOTVKRXluenlNYU8rQXFhWW1pSnUiLCJtYWMiOiIwYTI1ZTMyZDRmZmU3YWVlNGUwN2I2ZmIyZjVjMmIxZDg5NDA3MzQxYTMxZDAyOTZjMjc2N2Y0NzMwMjg3NThkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRhS0NRVzV6WEljTUlrUTl1SmNIaVE9PSIsInZhbHVlIjoidkExR2ZVcnNJZGw3QVZ6MU1iY2pRcHlTVFlNNW9xbW51L1pEb0hMWEZSdlI5YlpUMGtvQXJ2ZW9vTWp5aU8yOE9nWE0vMW1GaVpxaEpLZWhpd2lQQ3RwWStQYmc3aEVnc3VjSjJaSU5Uc1Y1aldyN09MdzlKRXJRSmVWN1kxcnciLCJtYWMiOiIwN2M1NjJhYjhhMzM3NmM3MWIyOTU1MzRhODFlY2MxMjFiNjE0YjliZWVlYzJjMTViNTc2ZmU5YmM0YTMwYTcxIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZE0YVLZQjR9JyraSrLfdfqjJhqLh1JxweQcNpzyV</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-304561111\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-120832877 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRhS0NRVzV6WEljTUlrUTl1SmNIaVE9PSIsInZhbHVlIjoidkExR2ZVcnNJZGw3QVZ6MU1iY2pRcHlTVFlNNW9xbW51L1pEb0hMWEZSdlI5YlpUMGtvQXJ2ZW9vTWp5aU8yOE9nWE0vMW1GaVpxaEpLZWhpd2lQQ3RwWStQYmc3aEVnc3VjSjJaSU5Uc1Y1aldyN09MdzlKRXJRSmVWN1kxcnciLCJtYWMiOiIwN2M1NjJhYjhhMzM3NmM3MWIyOTU1MzRhODFlY2MxMjFiNjE0YjliZWVlYzJjMTViNTc2ZmU5YmM0YTMwYTcxIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im5ndXltUFVBcnNBZjRyS1hvdEREUWc9PSIsInZhbHVlIjoiV1g0VWo5ZFQwQW5Xc215b294TzlYRGhZV0toMjdDZ3VrUkQrMS84ZWYwVno1T2hqQ3Z4RXk0TUkzeWo2QitKR0doaWhhNHZ6TW02aE5sYVRlNDdPOG1VdGhNbFFuU0UySlVtWUtIUC9IVHlHOTVKRXluenlNYU8rQXFhWW1pSnUiLCJtYWMiOiIwYTI1ZTMyZDRmZmU3YWVlNGUwN2I2ZmIyZjVjMmIxZDg5NDA3MzQxYTMxZDAyOTZjMjc2N2Y0NzMwMjg3NThkIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120832877\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-192215722 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 15:38:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-192215722\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-934139264 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-934139264\", {\"maxDepth\":0})</script>\n"}}