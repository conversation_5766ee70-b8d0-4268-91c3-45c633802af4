{"__meta": {"id": "Xeca898e23f36c62d3d76a40ff126a189", "datetime": "2024-11-28 14:05:00", "utime": **********.755884, "method": "GET", "uri": "/api/casinos/games?page=1&pageSize=30&searchTerm=&category=ao-vivo&provider=all", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.394853, "end": **********.755907, "duration": 1.3610539436340332, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": **********.394853, "relative_start": 0, "end": **********.783008, "relative_end": **********.783008, "duration": 0.3881549835205078, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.783019, "relative_start": 0.3881659507751465, "end": **********.755909, "relative_end": 1.9073486328125e-06, "duration": 0.9728899002075195, "duration_str": "973ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14333128, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/casinos/games", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@allGames", "namespace": null, "prefix": "api/casinos", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=388\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:388-414</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00079, "accumulated_duration_str": "790μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'ao-vivo') and `status` = 1", "type": "query", "params": [], "bindings": ["ao-vivo", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.80016, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/casinos/games", "status_code": "<pre class=sf-dump id=sf-dump-25158516 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-25158516\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-190133674 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>pageSize</span>\" => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"\n  \"<span class=sf-dump-key>searchTerm</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ao-vivo</span>\"\n  \"<span class=sf-dump-key>provider</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190133674\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-416924550 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-416924550\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-237475656 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6Ik9wWHJqNysvK1hNcTBCR09ERGxWL1E9PSIsInZhbHVlIjoiMlVBTVNSZ0RKNU9jUTFvL2Z6QlVKdVhwenlmRTFvakFTSzdxTjU5MmhkZWRWTHlwLy80Y3VKUkVkV2hJejZjK3FHUzcxMHB6aFZtT0IzTVdWa0trL0t1cHJJWTJOU2NtMGo2MTV4TmJIV1V4bjUwRTRwdkp6R0R0eHNMeUF4bFEiLCJtYWMiOiJmYmJiYjBkMGNkN2Q1YjY0ZThjZjg3MWY1ZWI2ODQ4MjM1ZGM1MTdjZWJlMzFkNzM3ZjljMWFhYTA0ZjRlNGMwIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6Imt2WkZobkRCZ25pWHhpWFhsZE1ZV0E9PSIsInZhbHVlIjoiNVIxM29rWjd2MldmRkpaSlNVMU5zRTBPM1hySHNaSmtNR3RTUzBDNlFVZFM1azYySDcvYjhNcFg1SzhicC8wQjRPMXdnSDg0Z3l0Y3Vsa3FVMmJNTHk0RyszdTU5Ti9LbnNIOFdWbkt3TmZjYUVMVUpndG1QWE8rZGRibXJyRG8iLCJtYWMiOiI2MjY5ZTMwMzEwNWIyMWU4OWFmOWZiODhkMWRhMjg5YWJkZTA4MmNiNTllZDQyMWIwODRhYTgwZGRhYTU5M2MwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">https://job.forradapg.com/casino/provider/all/category/ao-vivo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"133 characters\">Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9wWHJqNysvK1hNcTBCR09ERGxWL1E9PSIsInZhbHVlIjoiMlVBTVNSZ0RKNU9jUTFvL2Z6QlVKdVhwenlmRTFvakFTSzdxTjU5MmhkZWRWTHlwLy80Y3VKUkVkV2hJejZjK3FHUzcxMHB6aFZtT0IzTVdWa0trL0t1cHJJWTJOU2NtMGo2MTV4TmJIV1V4bjUwRTRwdkp6R0R0eHNMeUF4bFEiLCJtYWMiOiJmYmJiYjBkMGNkN2Q1YjY0ZThjZjg3MWY1ZWI2ODQ4MjM1ZGM1MTdjZWJlMzFkNzM3ZjljMWFhYTA0ZjRlNGMwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QZTB9Q66Z6TZdPmWFr3VIl4w177WleBhzBYndcG6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237475656\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1099901621 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9wWHJqNysvK1hNcTBCR09ERGxWL1E9PSIsInZhbHVlIjoiMlVBTVNSZ0RKNU9jUTFvL2Z6QlVKdVhwenlmRTFvakFTSzdxTjU5MmhkZWRWTHlwLy80Y3VKUkVkV2hJejZjK3FHUzcxMHB6aFZtT0IzTVdWa0trL0t1cHJJWTJOU2NtMGo2MTV4TmJIV1V4bjUwRTRwdkp6R0R0eHNMeUF4bFEiLCJtYWMiOiJmYmJiYjBkMGNkN2Q1YjY0ZThjZjg3MWY1ZWI2ODQ4MjM1ZGM1MTdjZWJlMzFkNzM3ZjljMWFhYTA0ZjRlNGMwIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Imt2WkZobkRCZ25pWHhpWFhsZE1ZV0E9PSIsInZhbHVlIjoiNVIxM29rWjd2MldmRkpaSlNVMU5zRTBPM1hySHNaSmtNR3RTUzBDNlFVZFM1azYySDcvYjhNcFg1SzhicC8wQjRPMXdnSDg0Z3l0Y3Vsa3FVMmJNTHk0RyszdTU5Ti9LbnNIOFdWbkt3TmZjYUVMVUpndG1QWE8rZGRibXJyRG8iLCJtYWMiOiI2MjY5ZTMwMzEwNWIyMWU4OWFmOWZiODhkMWRhMjg5YWJkZTA4MmNiNTllZDQyMWIwODRhYTgwZGRhYTU5M2MwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099901621\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-865236385 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 17:05:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865236385\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1992480017 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1992480017\", {\"maxDepth\":0})</script>\n"}}