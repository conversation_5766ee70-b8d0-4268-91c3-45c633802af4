{"__meta": {"id": "Xec9acd7903737696551b21f197b0decd", "datetime": "2024-11-28 12:26:28", "utime": 1732807588.000264, "method": "GET", "uri": "/api/casinos/games?category=popular", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732807570.593687, "end": 1732807588.000285, "duration": 17.40659785270691, "duration_str": "17.41s", "measures": [{"label": "Booting", "start": 1732807570.593687, "relative_start": 0, "end": **********.728972, "relative_end": **********.728972, "duration": 4.135284900665283, "duration_str": "4.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.728987, "relative_start": 4.135299921035767, "end": 1732807588.000287, "relative_end": 2.1457672119140625e-06, "duration": 13.271300077438354, "duration_str": "13.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14827560, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/casinos/games", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@allGames", "namespace": null, "prefix": "api/casinos", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=388\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:388-414</a>"}, "queries": {"nb_statements": 100, "nb_visible_statements": 100, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 1.02778, "accumulated_duration_str": "1.03s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'popular') and `status` = 1", "type": "query", "params": [], "bindings": ["popular", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.939009, "duration": 0.01225, "duration_str": "12.25ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 1.192}, {"sql": "select * from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'popular') and `status` = 1 order by `views` desc limit 30 offset 0", "type": "query", "params": [], "bindings": ["popular", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.0901248, "duration": 0.009349999999999999, "duration_str": "9.35ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 1.192, "width_percent": 0.91}, {"sql": "select * from `providers` where `providers`.`id` in (1, 15, 18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.223908, "duration": 0.02966, "duration_str": "29.66ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 2.102, "width_percent": 2.886}, {"sql": "select `categories`.*, `category_game`.`game_id` as `pivot_game_id`, `category_game`.`category_id` as `pivot_category_id` from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `category_game`.`game_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 972, 1101, 1103, 1126, 1141, 1149, 1150, 1152, 1155, 1160, 1163, 1164, 1167)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.339676, "duration": 0.023219999999999998, "duration_str": "23.22ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 4.987, "width_percent": 2.259}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1 limit 1", "type": "query", "params": [], "bindings": [********, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807583.57253, "duration": 0.02976, "duration_str": "29.76ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 7.247, "width_percent": 2.896}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807583.6525118, "duration": 0.02393, "duration_str": "23.93ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 10.142, "width_percent": 2.328}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1 limit 1", "type": "query", "params": [], "bindings": [********, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807583.712507, "duration": 0.02597, "duration_str": "25.97ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 12.471, "width_percent": 2.527}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807583.800658, "duration": 0.012539999999999999, "duration_str": "12.54ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 14.997, "width_percent": 1.22}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 2 limit 1", "type": "query", "params": [], "bindings": [********, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807583.883162, "duration": 0.01871, "duration_str": "18.71ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 16.217, "width_percent": 1.82}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 2 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807583.953194, "duration": 0.02828, "duration_str": "28.28ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 18.038, "width_percent": 2.752}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 2 limit 1", "type": "query", "params": [], "bindings": [********, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.004561, "duration": 0.01618, "duration_str": "16.18ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 20.789, "width_percent": 1.574}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 2 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.093369, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 22.364, "width_percent": 0.267}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 7 limit 1", "type": "query", "params": [], "bindings": [********, 7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.146975, "duration": 0.00677, "duration_str": "6.77ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 22.63, "width_percent": 0.659}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 7 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.205035, "duration": 0.01271, "duration_str": "12.71ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 23.289, "width_percent": 1.237}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 7 limit 1", "type": "query", "params": [], "bindings": [********, 7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.259234, "duration": 0.01405, "duration_str": "14.05ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 24.526, "width_percent": 1.367}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 7 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.322848, "duration": 0.01143, "duration_str": "11.43ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 25.893, "width_percent": 1.112}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 4 limit 1", "type": "query", "params": [], "bindings": [********, 4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.394506, "duration": 0.01328, "duration_str": "13.28ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 27.005, "width_percent": 1.292}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 4 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.468089, "duration": 0.01052, "duration_str": "10.52ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 28.297, "width_percent": 1.024}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 4 limit 1", "type": "query", "params": [], "bindings": [********, 4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.5165908, "duration": 0.03324, "duration_str": "33.24ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 29.32, "width_percent": 3.234}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 4 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.592451, "duration": 0.02509, "duration_str": "25.09ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 32.555, "width_percent": 2.441}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 3 limit 1", "type": "query", "params": [], "bindings": [********, 3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.660386, "duration": 0.02521, "duration_str": "25.21ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 34.996, "width_percent": 2.453}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 3 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.746021, "duration": 0.01066, "duration_str": "10.66ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 37.449, "width_percent": 1.037}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 3 limit 1", "type": "query", "params": [], "bindings": [********, 3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.791702, "duration": 0.01812, "duration_str": "18.12ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 38.486, "width_percent": 1.763}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 3 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.881824, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 40.249, "width_percent": 0.052}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 8 limit 1", "type": "query", "params": [], "bindings": [********, 8], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807584.9498909, "duration": 0.01586, "duration_str": "15.86ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 40.3, "width_percent": 1.543}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 8 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.0111191, "duration": 0.01471, "duration_str": "14.71ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 41.844, "width_percent": 1.431}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 8 limit 1", "type": "query", "params": [], "bindings": [********, 8], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.063474, "duration": 0.01461, "duration_str": "14.61ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 43.275, "width_percent": 1.422}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 8 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.1242502, "duration": 0.024149999999999998, "duration_str": "24.15ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 44.696, "width_percent": 2.35}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 6 limit 1", "type": "query", "params": [], "bindings": [********, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.2084398, "duration": 0.02114, "duration_str": "21.14ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 47.046, "width_percent": 2.057}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 6 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.288583, "duration": 0.02128, "duration_str": "21.28ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 49.103, "width_percent": 2.07}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 6 limit 1", "type": "query", "params": [], "bindings": [********, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.347248, "duration": 0.02223, "duration_str": "22.23ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 51.173, "width_percent": 2.163}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 6 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.408605, "duration": 0.02178, "duration_str": "21.78ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 53.336, "width_percent": 2.119}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 11 limit 1", "type": "query", "params": [], "bindings": [********, 11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.4978, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 55.455, "width_percent": 0.376}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 11 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.553389, "duration": 0.01209, "duration_str": "12.09ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 55.831, "width_percent": 1.176}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 11 limit 1", "type": "query", "params": [], "bindings": [********, 11], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.6220398, "duration": 0.01097, "duration_str": "10.97ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 57.007, "width_percent": 1.067}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 11 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.687593, "duration": 0.013869999999999999, "duration_str": "13.87ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 58.075, "width_percent": 1.35}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 5 limit 1", "type": "query", "params": [], "bindings": [********, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.765814, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 59.424, "width_percent": 0.037}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 5 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.819307, "duration": 0.0105, "duration_str": "10.5ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 59.461, "width_percent": 1.022}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 5 limit 1", "type": "query", "params": [], "bindings": [********, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.87556, "duration": 0.010199999999999999, "duration_str": "10.2ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 60.483, "width_percent": 0.992}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 5 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807585.942822, "duration": 0.00697, "duration_str": "6.97ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 61.475, "width_percent": 0.678}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1126 limit 1", "type": "query", "params": [], "bindings": [********, 1126], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.006898, "duration": 0.022510000000000002, "duration_str": "22.51ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 62.153, "width_percent": 2.19}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1126 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1126], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.083045, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 64.344, "width_percent": 0.42}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1126 limit 1", "type": "query", "params": [], "bindings": [********, 1126], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.143058, "duration": 0.01456, "duration_str": "14.56ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 64.764, "width_percent": 1.417}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1126 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1126], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.196564, "duration": 0.03184, "duration_str": "31.84ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 66.181, "width_percent": 3.098}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1152 limit 1", "type": "query", "params": [], "bindings": [********, 1152], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.2800088, "duration": 0.01377, "duration_str": "13.77ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 69.278, "width_percent": 1.34}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1152 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1152], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.361219, "duration": 0.009810000000000001, "duration_str": "9.81ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 70.618, "width_percent": 0.954}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1152 limit 1", "type": "query", "params": [], "bindings": [********, 1152], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.4153569, "duration": 0.01375, "duration_str": "13.75ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 71.573, "width_percent": 1.338}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1152 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1152], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.491042, "duration": 0.01492, "duration_str": "14.92ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 72.911, "width_percent": 1.452}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1101 limit 1", "type": "query", "params": [], "bindings": [********, 1101], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.554906, "duration": 0.01499, "duration_str": "14.99ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 74.362, "width_percent": 1.458}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1101 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1101], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.6299071, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 75.821, "width_percent": 0.373}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1101 limit 1", "type": "query", "params": [], "bindings": [********, 1101], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.6688712, "duration": 0.01662, "duration_str": "16.62ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 76.193, "width_percent": 1.617}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1101 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1101], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.7259312, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 77.81, "width_percent": 0.353}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1150 limit 1", "type": "query", "params": [], "bindings": [********, 1150], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.7578769, "duration": 0.00531, "duration_str": "5.31ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 78.164, "width_percent": 0.517}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1150 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1150], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.792254, "duration": 0.00584, "duration_str": "5.84ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 78.68, "width_percent": 0.568}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1150 limit 1", "type": "query", "params": [], "bindings": [********, 1150], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.827496, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 79.248, "width_percent": 0.406}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1150 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1150], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.857058, "duration": 0.00874, "duration_str": "8.74ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 79.654, "width_percent": 0.85}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1160 limit 1", "type": "query", "params": [], "bindings": [********, 1160], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.897663, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 80.505, "width_percent": 0.044}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1160 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1160], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.9342148, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 80.548, "width_percent": 0.059}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1160 limit 1", "type": "query", "params": [], "bindings": [********, 1160], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.952454, "duration": 0.01209, "duration_str": "12.09ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 80.608, "width_percent": 1.176}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1160 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1160], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807586.9869869, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 81.784, "width_percent": 0.247}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1167 limit 1", "type": "query", "params": [], "bindings": [********, 1167], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.011318, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 82.031, "width_percent": 0.209}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1167 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1167], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.038733, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 82.24, "width_percent": 0.038}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1167 limit 1", "type": "query", "params": [], "bindings": [********, 1167], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.055679, "duration": 0.00608, "duration_str": "6.08ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 82.278, "width_percent": 0.592}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1167 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1167], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.076389, "duration": 0.0079, "duration_str": "7.9ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 82.87, "width_percent": 0.769}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 9 limit 1", "type": "query", "params": [], "bindings": [********, 9], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.107968, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 83.639, "width_percent": 0.166}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 9 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.124799, "duration": 0.00875, "duration_str": "8.75ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 83.805, "width_percent": 0.851}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 9 limit 1", "type": "query", "params": [], "bindings": [********, 9], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.15263, "duration": 0.00522, "duration_str": "5.22ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 84.656, "width_percent": 0.508}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 9 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.186428, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 85.164, "width_percent": 0.065}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1141 limit 1", "type": "query", "params": [], "bindings": [********, 1141], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.2109282, "duration": 0.0065899999999999995, "duration_str": "6.59ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 85.229, "width_percent": 0.641}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1141 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1141], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.2460742, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 85.871, "width_percent": 0.198}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1141 limit 1", "type": "query", "params": [], "bindings": [********, 1141], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.2667668, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 86.068, "width_percent": 0.28}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1141 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1141], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.282525, "duration": 0.00544, "duration_str": "5.44ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 86.348, "width_percent": 0.529}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1164 limit 1", "type": "query", "params": [], "bindings": [********, 1164], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.312125, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 86.878, "width_percent": 0.151}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1164 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1164], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.3332932, "duration": 0.00468, "duration_str": "4.68ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 87.028, "width_percent": 0.455}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1164 limit 1", "type": "query", "params": [], "bindings": [********, 1164], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.361155, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 87.484, "width_percent": 0.055}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1164 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1164], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.379895, "duration": 0.0072699999999999996, "duration_str": "7.27ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 87.539, "width_percent": 0.707}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 972 limit 1", "type": "query", "params": [], "bindings": [********, 972], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.408549, "duration": 0.0052699999999999995, "duration_str": "5.27ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 88.247, "width_percent": 0.513}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 972 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [972], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.433216, "duration": 0.004860000000000001, "duration_str": "4.86ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 88.759, "width_percent": 0.473}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 972 limit 1", "type": "query", "params": [], "bindings": [********, 972], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.449163, "duration": 0.007940000000000001, "duration_str": "7.94ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 89.232, "width_percent": 0.773}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 972 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [972], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.478981, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 90.005, "width_percent": 0.241}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1103 limit 1", "type": "query", "params": [], "bindings": [********, 1103], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.501243, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 90.246, "width_percent": 0.442}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1103 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1103], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.530067, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 90.688, "width_percent": 0.314}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1103 limit 1", "type": "query", "params": [], "bindings": [********, 1103], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.5537999, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 91.002, "width_percent": 0.361}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1103 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1103], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.575602, "duration": 0.00653, "duration_str": "6.53ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 91.363, "width_percent": 0.635}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1155 limit 1", "type": "query", "params": [], "bindings": [********, 1155], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.6007779, "duration": 0.0092, "duration_str": "9.2ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 91.998, "width_percent": 0.895}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1155 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1155], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.6291358, "duration": 0.009550000000000001, "duration_str": "9.55ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 92.893, "width_percent": 0.929}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1155 limit 1", "type": "query", "params": [], "bindings": [********, 1155], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.657267, "duration": 0.00858, "duration_str": "8.58ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 93.823, "width_percent": 0.835}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1155 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1155], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.6938179, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 94.657, "width_percent": 0.055}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1149 limit 1", "type": "query", "params": [], "bindings": [********, 1149], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.717061, "duration": 0.00477, "duration_str": "4.77ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 94.713, "width_percent": 0.464}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1149 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1149], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.746548, "duration": 0.00737, "duration_str": "7.37ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 95.177, "width_percent": 0.717}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1149 limit 1", "type": "query", "params": [], "bindings": [********, 1149], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.771644, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 95.894, "width_percent": 0.203}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1149 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1149], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.79236, "duration": 0.0079, "duration_str": "7.9ms", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 96.097, "width_percent": 0.769}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 1163 limit 1", "type": "query", "params": [], "bindings": [********, 1163], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.818116, "duration": 0.00656, "duration_str": "6.56ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 96.866, "width_percent": 0.638}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 1163 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [1163], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.8512778, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 97.504, "width_percent": 0.058}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 1163 limit 1", "type": "query", "params": [], "bindings": [********, 1163], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.874209, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 97.563, "width_percent": 0.058}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 1163 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [1163], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.906303, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 97.621, "width_percent": 0.052}, {"sql": "select * from `game_favorites` where `user_id` = ******** and `game_id` = 10 limit 1", "type": "query", "params": [], "bindings": [********, 10], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.925357, "duration": 0.01458, "duration_str": "14.58ms", "memory": 0, "memory_str": null, "filename": "Game.php:96", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=96", "ajax": false, "filename": "Game.php", "line": "96"}, "connection": "china15tema2", "explain": null, "start_percent": 97.673, "width_percent": 1.419}, {"sql": "select count(*) as aggregate from `game_favorites` where `game_favorites`.`game_id` = 10 and `game_favorites`.`game_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.955576, "duration": 0.00623, "duration_str": "6.23ms", "memory": 0, "memory_str": null, "filename": "Game.php:61", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=61", "ajax": false, "filename": "Game.php", "line": "61"}, "connection": "china15tema2", "explain": null, "start_percent": 99.091, "width_percent": 0.606}, {"sql": "select * from `game_likes` where `user_id` = ******** and `game_id` = 10 limit 1", "type": "query", "params": [], "bindings": [********, 10], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 31, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.971087, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "Game.php:80", "source": {"index": 16, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=80", "ajax": false, "filename": "Game.php", "line": "80"}, "connection": "china15tema2", "explain": null, "start_percent": 99.697, "width_percent": 0.262}, {"sql": "select count(*) as aggregate from `game_likes` where `game_likes`.`game_id` = 10 and `game_likes`.`game_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 196}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Pagination/LengthAwarePaginator.php", "line": 218}, {"index": 34, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/symfony/http-foundation/JsonResponse.php", "line": 49}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32}], "start": 1732807587.98664, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Game.php:70", "source": {"index": 19, "namespace": null, "name": "app/Models/Game.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Models/Game.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=70", "ajax": false, "filename": "Game.php", "line": "70"}, "connection": "china15tema2", "explain": null, "start_percent": 99.959, "width_percent": 0.041}]}, "models": {"data": {"App\\Models\\Category": {"value": 95, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Game": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FGame.php&line=1", "ajax": false, "filename": "Game.php", "line": "?"}}, "App\\Models\\Provider": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FModels%2FProvider.php&line=1", "ajax": false, "filename": "Provider.php", "line": "?"}}}, "count": 122, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/casinos/games", "status_code": "<pre class=sf-dump id=sf-dump-835473217 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-835473217\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">popular</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-325906520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-325906520\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-169651246 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IkdibkJzV0hqcU1VMHNUME9aS0pvQVE9PSIsInZhbHVlIjoiTHhTVDV3YVU5Q2VXM0lDSzNQRGtTaGdBekVSUU1obHFmNzJJcmdmcHUzK1BQNlgyUjB6N0tOU0RBY3d5eFpncEFlUWZyTm5iUnJ0RUh4S09QcWZXRnFBS2N5b1dZZm5JS3kzRmdRaVd6Wkl4OFNDK1k4WnFVSmpWdFRFQitUN08iLCJtYWMiOiI1NGJiYjA5MTk2NzNiZDE3OTg0Mzc1ZDVmZmUyZDc2MjU1Njg3YjU3NTRlN2E4NWViNDAxYjc1ZjdmYTM3Nzc3IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6Im1xSmh5d1pFUm10eHRvdjFoaStRbWc9PSIsInZhbHVlIjoib3RqVW4wS3VGKy8zcVdkalVGRWZrbzcxT0EzRmFkTFZ4WnFvZkxpWkJSajV4OW4wRnlRVkRKQURPdG9RNzVmQUs4RnFPUW92WlpzbkNmWkkvTmxySm9jUnZjMmhidVEyQ0Q1dDh1VFRWWk8wc2kyQlh6VU44bkJrdktKb0RuZjkiLCJtYWMiOiJlZDMxODQ2Mjg3YWJlNTAzZDM1OTg1YTNkMGMzZTkwMTM3N2I4YTFkODA5NmQ5OWYzZWEwYmUwMzBhMzZiMWZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer eyJ0e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdibkJzV0hqcU1VMHNUME9aS0pvQVE9PSIsInZhbHVlIjoiTHhTVDV3YVU5Q2VXM0lDSzNQRGtTaGdBekVSUU1obHFmNzJJcmdmcHUzK1BQNlgyUjB6N0tOU0RBY3d5eFpncEFlUWZyTm5iUnJ0RUh4S09QcWZXRnFBS2N5b1dZZm5JS3kzRmdRaVd6Wkl4OFNDK1k4WnFVSmpWdFRFQitUN08iLCJtYWMiOiI1NGJiYjA5MTk2NzNiZDE3OTg0Mzc1ZDVmZmUyZDc2MjU1Njg3YjU3NTRlN2E4NWViNDAxYjc1ZjdmYTM3Nzc3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZE0YVLZQjR9JyraSrLfdfqjJhqLh1JxweQcNpzyV</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169651246\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-936970133 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdibkJzV0hqcU1VMHNUME9aS0pvQVE9PSIsInZhbHVlIjoiTHhTVDV3YVU5Q2VXM0lDSzNQRGtTaGdBekVSUU1obHFmNzJJcmdmcHUzK1BQNlgyUjB6N0tOU0RBY3d5eFpncEFlUWZyTm5iUnJ0RUh4S09QcWZXRnFBS2N5b1dZZm5JS3kzRmdRaVd6Wkl4OFNDK1k4WnFVSmpWdFRFQitUN08iLCJtYWMiOiI1NGJiYjA5MTk2NzNiZDE3OTg0Mzc1ZDVmZmUyZDc2MjU1Njg3YjU3NTRlN2E4NWViNDAxYjc1ZjdmYTM3Nzc3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im1xSmh5d1pFUm10eHRvdjFoaStRbWc9PSIsInZhbHVlIjoib3RqVW4wS3VGKy8zcVdkalVGRWZrbzcxT0EzRmFkTFZ4WnFvZkxpWkJSajV4OW4wRnlRVkRKQURPdG9RNzVmQUs4RnFPUW92WlpzbkNmWkkvTmxySm9jUnZjMmhidVEyQ0Q1dDh1VFRWWk8wc2kyQlh6VU44bkJrdktKb0RuZjkiLCJtYWMiOiJlZDMxODQ2Mjg3YWJlNTAzZDM1OTg1YTNkMGMzZTkwMTM3N2I4YTFkODA5NmQ5OWYzZWEwYmUwMzBhMzZiMWZmIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-936970133\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-308151414 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 15:26:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-308151414\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1554884696 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1554884696\", {\"maxDepth\":0})</script>\n"}}