{"__meta": {"id": "Xdc9cf918dd6b9a2b3132ff665548b8a8", "datetime": "2024-11-28 17:06:42", "utime": 1732824402.65559, "method": "POST", "uri": "/playfiver/webhook", "ip": "************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732824397.803361, "end": 1732824402.655608, "duration": 4.852246999740601, "duration_str": "4.85s", "measures": [{"label": "Booting", "start": 1732824397.803361, "relative_start": 0, "end": 1732824399.730428, "relative_end": 1732824399.730428, "duration": 1.9270670413970947, "duration_str": "1.93s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732824399.730444, "relative_start": 1.9270830154418945, "end": 1732824402.655611, "relative_end": 3.0994415283203125e-06, "duration": 2.9251670837402344, "duration_str": "2.93s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14082704, "peak_usage_str": "13MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST playfiver/webhook", "middleware": "web", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@webhookPlayFiver", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=424\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:424-427</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "j4g82e0k3SWXmlPCETUcGDSB6l4XllyEr70L7Y4C", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/playfiver/webhook", "status_code": "<pre class=sf-dump id=sf-dump-1654414368 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1654414368\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-526810126 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-526810126\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-837490363 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">BALANCE</span>\"\n  \"<span class=sf-dump-key>user_code</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837490363\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-933612038 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">GuzzleHttp/7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933612038\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-16268023 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-16268023\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1231354743 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 20:06:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IktzZHUwam16QjVmcGdBbkFMV1gzeUE9PSIsInZhbHVlIjoiUW4yTHhvMlUybFpkdjIvS2dWdDNPRVBQN1J6MWhseUt3VkRNa0NhZFFkRW55a2ZBT3hLYXEzemJ5cHc4Z2lFZ2JqZHNFOFA1QXFZbzkxZUFxRVp0emhXUzhDbGVMZmN3bkM0Z1NpUjJMaXBONDZRcGxJQXYrRnM5czdORkNsNzMiLCJtYWMiOiI4ZDUzYWQwNzI4Njk3OTA0YWVkYjFlNDlmMDFjNjRlNDBlODFkMGVjNTZlMTg2ZDAwYTViMDI1ODM5MjIyMzc2IiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 22:06:42 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6ImhhUXhSa0FRdnh4UGoySkRJR3V0VFE9PSIsInZhbHVlIjoiang3TG04M2lZVHB6NnE1VVU4TzNHV25wUGFFQXcvSEpScmljMyszTjVxT1RTbnhaSERuWHFLb241K3pucUhxNVNmeWV0MVBQRGxYK3dGLzE5eGlaYS9yUDA4V1p4ZWt6eVk5MERqVEppU05tZEQ3K0tWUVBGb2taVmJMMFYyc0QiLCJtYWMiOiIwZjg4NmMyZWE2YWJiMDU0ZjgyZmJkYzY0MzA1YWFlZGY1NmY1NWRjYzZlNDc5NWUxNTg0YzBhNGU0ZTE5OGY5IiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 22:06:42 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IktzZHUwam16QjVmcGdBbkFMV1gzeUE9PSIsInZhbHVlIjoiUW4yTHhvMlUybFpkdjIvS2dWdDNPRVBQN1J6MWhseUt3VkRNa0NhZFFkRW55a2ZBT3hLYXEzemJ5cHc4Z2lFZ2JqZHNFOFA1QXFZbzkxZUFxRVp0emhXUzhDbGVMZmN3bkM0Z1NpUjJMaXBONDZRcGxJQXYrRnM5czdORkNsNzMiLCJtYWMiOiI4ZDUzYWQwNzI4Njk3OTA0YWVkYjFlNDlmMDFjNjRlNDBlODFkMGVjNTZlMTg2ZDAwYTViMDI1ODM5MjIyMzc2IiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 22:06:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6ImhhUXhSa0FRdnh4UGoySkRJR3V0VFE9PSIsInZhbHVlIjoiang3TG04M2lZVHB6NnE1VVU4TzNHV25wUGFFQXcvSEpScmljMyszTjVxT1RTbnhaSERuWHFLb241K3pucUhxNVNmeWV0MVBQRGxYK3dGLzE5eGlaYS9yUDA4V1p4ZWt6eVk5MERqVEppU05tZEQ3K0tWUVBGb2taVmJMMFYyc0QiLCJtYWMiOiIwZjg4NmMyZWE2YWJiMDU0ZjgyZmJkYzY0MzA1YWFlZGY1NmY1NWRjYzZlNDc5NWUxNTg0YzBhNGU0ZTE5OGY5IiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 22:06:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231354743\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-106822606 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">j4g82e0k3SWXmlPCETUcGDSB6l4XllyEr70L7Y4C</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106822606\", {\"maxDepth\":0})</script>\n"}}