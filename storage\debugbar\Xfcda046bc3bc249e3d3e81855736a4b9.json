{"__meta": {"id": "Xfcda046bc3bc249e3d3e81855736a4b9", "datetime": "2024-11-28 16:58:50", "utime": 1732823930.961187, "method": "GET", "uri": "/games/play/2/98", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732823929.62004, "end": 1732823930.961209, "duration": 1.3411691188812256, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1732823929.62004, "relative_start": 0, "end": 1732823929.998293, "relative_end": 1732823929.998293, "duration": 0.3782529830932617, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1732823929.998315, "relative_start": 0.37827515602111816, "end": 1732823930.961212, "relative_end": 2.86102294921875e-06, "duration": 0.9628968238830566, "duration_str": "963ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14215888, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "layouts.app", "param_count": null, "params": [], "start": 1732823930.948858, "type": "blade", "hash": "blade/home/<USER>/htdocs/job.forradapg.com/resources/views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "route": {"uri": "GET {view}", "middleware": "web", "uses": "App\\Http\\Controllers\\Layouts\\ApplicationController@__invoke", "controller": "App\\Http\\Controllers\\Layouts\\ApplicationController", "namespace": null, "prefix": "", "where": []}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QZTB9Q66Z6TZdPmWFr3VIl4w177WleBhzBYndcG6", "_previous": "array:1 [\n  \"url\" => \"https://job.forradapg.com/games/play/2/98\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/games/play/2/98", "status_code": "<pre class=sf-dump id=sf-dump-1211139480 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1211139480\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-52810026 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-52810026\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-283953316 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-283953316\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2083864503 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IlkvT3NobFZPNU04dXhYNUdHUEYwUHc9PSIsInZhbHVlIjoidUNYR2VKV3NSUzJsSlYvSDJWOXZ2NVpVcjBsellkZmxHVVZmVHFYMERtVjg3YldFUzhVb29yVmJLdkszWFRlUGY4Yll0UFczbThDUlR6WXhhYU1KeWJoL3JubXB1N2pZRUt6VWZkemgxTnZkMFZOVWhKQ1BGcjRhdlBoT2xxNjUiLCJtYWMiOiI2MjA1ZjU4YjhiMjRkMWNmNzJhYTNkMjJjNGRiYWEzNWUyOWY5ZDlhODJjMjZiMzBhOGRjNjJiZDhkZjc2NDY5IiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6IlNGOU5NZ2gxL1FWUkZGc2EzRkdkbUE9PSIsInZhbHVlIjoiSVRuT0FPazRVdFFBNHJqbFM4Yjh0M2xMbWhIYWJrV0ViRnZvNFppdnBpcUh3QVRMdWZlTm50Vk9tRXdmYmJkbGhJSlpCaGpSOUt5YWt0eHVteUJWYU5FNEI2c2JDTm01Vk9pNzFrbmRlTzlUL2d3aTdKVHFxL1JwMnB0V091RG8iLCJtYWMiOiJkNjRmY2E3M2NmYWMwMTJjZDRhOTI2MGIyNTNkODc1OTRlOGMxMzIyYjlhZWIwZWM0YjYyM2EzYjZlOTkxMjM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"133 characters\">Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083864503\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2128768891 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QZTB9Q66Z6TZdPmWFr3VIl4w177WleBhzBYndcG6</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ouMgLWZDfglZboABD5ehLF6qVbpM89qCfI39mpj0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128768891\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-818025008 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 19:58:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik9GVzFhTk5FUVdMZm5jVldZUGtkV3c9PSIsInZhbHVlIjoidnlOK1NsejREandkWDBGRnNyTWVQNmxSZmdnQ1BFN1BJeEpqYWN6ZkxmWWlac1Y4TXZFQUxyT3lyenBQVnpCakg3eGR0dCt5RWZtWWJxY216WXBweHFXR0ZrWk1Sd2NLUENtU2lhcERPL3FVWXZzbFJ0cEc3QjFETDlTWnBTNFQiLCJtYWMiOiI0M2ZlNzkzZmE0Mzk0ZjA2OGUzZGNjZGQ5MThmZjM1ODAyNWJkN2NhMWQwNTllNDgxNWZmMmFlNjAwOTVlMDY5IiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 21:58:50 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">gold777pg_session=eyJpdiI6IktBanBubFkyd1NvNFlJejRTVzVkTkE9PSIsInZhbHVlIjoiWkQ2ekFuMyszampWN0NCVHM3SXRrYVpHTXZPdXpiRlVHaU5PSDRNQldiNzhzNnhoS0EzOENFWitWWlFNSkRIRW5KUkExZ01zQ2xOeTlqdHZpTndCSWJLSUpjOU03L2ZIMk1vVVYyM1I4TDZMd2M5eEdKVldvM0JNSmxMSVNVeGsiLCJtYWMiOiI1NjVlZDEyYTViMzNjNjVlNDllMjI2M2YzYWU5MzRhY2ViYTg0NTlkNDY1NGU3MDcwODdkN2RmYzE0Y2E1ZDg3IiwidGFnIjoiIn0%3D; expires=Thu, 28 Nov 2024 21:58:50 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik9GVzFhTk5FUVdMZm5jVldZUGtkV3c9PSIsInZhbHVlIjoidnlOK1NsejREandkWDBGRnNyTWVQNmxSZmdnQ1BFN1BJeEpqYWN6ZkxmWWlac1Y4TXZFQUxyT3lyenBQVnpCakg3eGR0dCt5RWZtWWJxY216WXBweHFXR0ZrWk1Sd2NLUENtU2lhcERPL3FVWXZzbFJ0cEc3QjFETDlTWnBTNFQiLCJtYWMiOiI0M2ZlNzkzZmE0Mzk0ZjA2OGUzZGNjZGQ5MThmZjM1ODAyNWJkN2NhMWQwNTllNDgxNWZmMmFlNjAwOTVlMDY5IiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 21:58:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">gold777pg_session=eyJpdiI6IktBanBubFkyd1NvNFlJejRTVzVkTkE9PSIsInZhbHVlIjoiWkQ2ekFuMyszampWN0NCVHM3SXRrYVpHTXZPdXpiRlVHaU5PSDRNQldiNzhzNnhoS0EzOENFWitWWlFNSkRIRW5KUkExZ01zQ2xOeTlqdHZpTndCSWJLSUpjOU03L2ZIMk1vVVYyM1I4TDZMd2M5eEdKVldvM0JNSmxMSVNVeGsiLCJtYWMiOiI1NjVlZDEyYTViMzNjNjVlNDllMjI2M2YzYWU5MzRhY2ViYTg0NTlkNDY1NGU3MDcwODdkN2RmYzE0Y2E1ZDg3IiwidGFnIjoiIn0%3D; expires=Thu, 28-Nov-2024 21:58:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818025008\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-901993055 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QZTB9Q66Z6TZdPmWFr3VIl4w177WleBhzBYndcG6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">https://job.forradapg.com/games/play/2/98</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-901993055\", {\"maxDepth\":0})</script>\n"}}