{"__meta": {"id": "Xf191b89d1c018e057b24a0a0aab07062", "datetime": "2024-11-28 11:32:50", "utime": 1732804370.660571, "method": "GET", "uri": "/api/casinos/games?category=ao-vivo", "ip": "*************"}, "php": {"version": "8.2.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1732804360.190652, "end": 1732804370.660599, "duration": 10.469947099685669, "duration_str": "10.47s", "measures": [{"label": "Booting", "start": 1732804360.190652, "relative_start": 0, "end": **********.58294, "relative_end": **********.58294, "duration": 2.3922882080078125, "duration_str": "2.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.582957, "relative_start": 2.3923051357269287, "end": 1732804370.660602, "relative_end": 3.0994415283203125e-06, "duration": 8.077645063400269, "duration_str": "8.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14316776, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/casinos/games", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\Games\\GameController@allGames", "namespace": null, "prefix": "api/casinos", "where": [], "file": "<a href=\"phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=388\" onclick=\"\">app/Http/Controllers/Api/Games/GameController.php:388-414</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03668, "accumulated_duration_str": "36.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `games` where exists (select * from `categories` inner join `category_game` on `categories`.`id` = `category_game`.`category_id` where `games`.`id` = `category_game`.`game_id` and `slug` = 'ao-vivo') and `status` = 1", "type": "query", "params": [], "bindings": ["ao-vivo", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/home/<USER>/htdocs/job.forradapg.com/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.696497, "duration": 0.03668, "duration_str": "36.68ms", "memory": 0, "memory_str": null, "filename": "GameController.php:411", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Games/GameController.php", "file": "/home/<USER>/htdocs/job.forradapg.com/app/Http/Controllers/Api/Games/GameController.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2Fhome%2Fforradapg-job%2Fhtdocs%2Fjob.forradapg.com%2Fapp%2FHttp%2FControllers%2FApi%2FGames%2FGameController.php&line=411", "ajax": false, "filename": "GameController.php", "line": "411"}, "connection": "china15tema2", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/casinos/games", "status_code": "<pre class=sf-dump id=sf-dump-737364415 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-737364415\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-919975416 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ao-vivo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919975416\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-784997482 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-784997482\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2085263005 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6Im9GVlhWUWhIR0luVnhEVEFlRzIzamc9PSIsInZhbHVlIjoiNnM4N0RBVnFCeFdPbFFTOXlWZW5aOXN1ODBxTnduYUF3WU9yQitaK2M1MERFT0pHc2NCREtvcE9VVCtVN2krNWh2STBGbEhraTNkdnI2TUxHR05McTVWb1I0QlpPcDJPWjhCeWpsZHNVcVNxRG1IS3crQ2VqSTFCY2kxWVNORUkiLCJtYWMiOiJmOWJmMjk5ZTBjMTA2MDc2ZTA1ZWRkMTNlYmM4NzJlNDA5ZDQxY2M5ODljMzQ3ZDYzZGZmYmNiZGU0YWQ2NDNhIiwidGFnIjoiIn0%3D; gold777pg_session=eyJpdiI6InZwcEdpdmh0RDFXcFIrdmFkWEk3SWc9PSIsInZhbHVlIjoicTRCZTVwRGo1a1BlMFZyUWxmRHBXaGRFbTBudHB2NFZLdlBLR05uWFdEQmhqODhwUWdFK2V2OFFLTlRzWTY1WmFXVGp6bjA5MEcwMVo5TVBUYzQ5SzZoemNIT1krczNTc09WN0tWaVQyYjZiZkZqeis4SGNDTUxDVm54dmVCYTciLCJtYWMiOiI2M2NjMWIxOTEyMjhjOGM0NzNjNGM0MzZkNDU2ODlhMzFkYmRjOWRjOWYyZWY2NDE1ZGJlNTgxM2E1Y2I2NDM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">pt-BR,pt;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://job.forradapg.com/home/<USER>/span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;131&quot;, &quot;Chromium&quot;;v=&quot;131&quot;, &quot;Not_A Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im9GVlhWUWhIR0luVnhEVEFlRzIzamc9PSIsInZhbHVlIjoiNnM4N0RBVnFCeFdPbFFTOXlWZW5aOXN1ODBxTnduYUF3WU9yQitaK2M1MERFT0pHc2NCREtvcE9VVCtVN2krNWh2STBGbEhraTNkdnI2TUxHR05McTVWb1I0QlpPcDJPWjhCeWpsZHNVcVNxRG1IS3crQ2VqSTFCY2kxWVNORUkiLCJtYWMiOiJmOWJmMjk5ZTBjMTA2MDc2ZTA1ZWRkMTNlYmM4NzJlNDA5ZDQxY2M5ODljMzQ3ZDYzZGZmYmNiZGU0YWQ2NDNhIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZxogPn2IBCCd0wbASYCv5voRQyu5lNXDFz0pAZq2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-for</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-real-ip</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-forwarded-host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">job.forradapg.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085263005\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-150764723 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im9GVlhWUWhIR0luVnhEVEFlRzIzamc9PSIsInZhbHVlIjoiNnM4N0RBVnFCeFdPbFFTOXlWZW5aOXN1ODBxTnduYUF3WU9yQitaK2M1MERFT0pHc2NCREtvcE9VVCtVN2krNWh2STBGbEhraTNkdnI2TUxHR05McTVWb1I0QlpPcDJPWjhCeWpsZHNVcVNxRG1IS3crQ2VqSTFCY2kxWVNORUkiLCJtYWMiOiJmOWJmMjk5ZTBjMTA2MDc2ZTA1ZWRkMTNlYmM4NzJlNDA5ZDQxY2M5ODljMzQ3ZDYzZGZmYmNiZGU0YWQ2NDNhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>gold777pg_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZwcEdpdmh0RDFXcFIrdmFkWEk3SWc9PSIsInZhbHVlIjoicTRCZTVwRGo1a1BlMFZyUWxmRHBXaGRFbTBudHB2NFZLdlBLR05uWFdEQmhqODhwUWdFK2V2OFFLTlRzWTY1WmFXVGp6bjA5MEcwMVo5TVBUYzQ5SzZoemNIT1krczNTc09WN0tWaVQyYjZiZkZqeis4SGNDTUxDVm54dmVCYTciLCJtYWMiOiI2M2NjMWIxOTEyMjhjOGM0NzNjNGM0MzZkNDU2ODlhMzFkYmRjOWRjOWYyZWY2NDE1ZGJlNTgxM2E1Y2I2NDM2IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150764723\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-760453430 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">private, must-revalidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Nov 2024 14:32:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760453430\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2063640733 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2063640733\", {\"maxDepth\":0})</script>\n"}}